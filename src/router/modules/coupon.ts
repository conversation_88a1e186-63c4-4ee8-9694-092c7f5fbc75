import type { RouteRecordRaw } from 'vue-router'

const coupons: RouteRecordRaw = {
  path: '/coupons',
  component: () => import('@/layouts/index.vue'),
  redirect: '/coupons/list',
  name: 'CouponsManagement',
  meta: {
    title: '优惠券管理',
    icon: 'ep:ticket',
    // auth: '/adm_coupon',
  },
  children: [
    {
      path: 'list',
      name: 'CouponsList',
      component: () => import('@/views/coupons/index.vue'),
      meta: {
        title: '优惠券列表',
        icon: 'ep:list',
        // auth: '/adm_coupon/list',
        keepAlive: true,
      },
    },
    {
      path: 'detail/:id',
      name: 'CouponsDetail',
      component: () => import('@/views/coupons/detail/index.vue'),
      meta: {
        title: '优惠券详情',
        icon: 'ep:view',
        // auth: '/adm_coupon/detail',
        menu: false,
        breadcrumb: true,
        activeMenu: '/coupons/list',
      },
    },
    {
      path: 'add',
      name: 'CouponsAdd',
      component: () => import('@/views/coupons/baseConfig/index.vue'),
      meta: {
        title: '新增优惠券',
        icon: 'ep:plus',
        // auth: '/adm_coupon/add',
        breadcrumb: true,
        activeMenu: '/coupons/list',
      },
    },

    // {
    //   path: 'old',
    //   name: 'CouponsOld',
    //   component: () => import('@/views/coupon/index.vue') as any,
    //   meta: {
    //     title: '优惠券管理（原版）',
    //     icon: 'ep:ticket',
    //     // auth: '/adm_coupon/query',
    //     keepAlive: true,
    //     activeMenu: '/coupons',
    //   },
    // },
  ],
}

export default coupons
