# 优惠券票务设置组件 (addCounponSetting_Ticket.vue)

## 概述

这是一个用于配置优惠券票务限制条件的Vue 3组件，支持影院、影厅、影片、时段等多维度的限制设置。

## 功能特性

- ✅ **影院限制**: 支持不限、指定影院、排除影院三种模式
- ✅ **影厅选择**: 为每个选中的影院配置具体的影厅限制
- ✅ **影片限制**: 支持不限、指定影片、最低限价影片
- ✅ **版本限制**: 支持影片版本的选择和配置
- ✅ **时段限制**: 支持时间段的配置
- ✅ **数据转换**: 自动处理组件格式与API格式的转换
- ✅ **表单验证**: 内置数据验证功能
- ✅ **TypeScript**: 完整的类型定义支持

## 使用方法

### 基础用法

```vue
<template>
  <div>
    <addCounponSetting_Ticket
      v-model="couponData"
      :page-mode="pageMode"
      @validate="handleValidate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import addCounponSetting_Ticket from './module/addCounponSetting_Ticket.vue'
import type { CouponFormData } from '@/utils/couponDataTransform'

const pageMode = ref<'add' | 'edit' | 'view'>('add')

const couponData = ref<CouponFormData>({
  cinemaScope: 0,
  cinemaIds: [],
  filmIds: [],
  filmScope: 0,
  lowestPrice: 0,
  filmVersions: [],
  periodAddBos: [],
})

function handleValidate(isValid: boolean, errors: string[]) {
  console.log('表单验证结果:', isValid, errors)
}
</script>
```

### 获取API格式数据

```vue
<script setup lang="ts">
import { ref } from 'vue'

const ticketSettingRef = ref()

// 获取API格式的数据
function getAPIData() {
  const apiData = ticketSettingRef.value?.getAPIData('coupon-id-123')
  console.log('API格式数据:', apiData)
  
  // 发送到后端
  // await submitCouponRestriction(apiData)
}

// 验证表单
function validateForm() {
  const validation = ticketSettingRef.value?.validateForm()
  if (!validation.isValid) {
    console.error('验证失败:', validation.errors)
    return false
  }
  return true
}
</script>

<template>
  <addCounponSetting_Ticket
    ref="ticketSettingRef"
    v-model="couponData"
  />
</template>
```

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modelValue | `CouponFormData` | - | 双向绑定的表单数据 |
| pageMode | `'add' \| 'edit' \| 'view'` | `'add'` | 页面模式，view模式下表单只读 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | `(value: CouponFormData)` | 表单数据变化时触发 |
| validate | `(isValid: boolean, errors: string[])` | 表单验证结果变化时触发 |

## 暴露的方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getAPIData | `couponId?: string` | `TicketRestrictionAPI` | 获取API格式的数据 |
| validateForm | - | `{isValid: boolean, errors: string[]}` | 验证表单数据 |

## 数据结构

### CouponFormData (组件内部格式)

```typescript
interface CouponFormData {
  cinemaScope: number        // 0: 不限, 1: 指定影院, 2: 排除影院
  cinemaIds: CinemaFormItem[] // 选中的影院列表
  filmIds: any[]             // 选中的影片列表
  filmScope: number          // 0: 不限, 1: 指定影片, 2: 最低价影片
  lowestPrice: number        // 最低限价
  filmVersions: any[]        // 影片版本列表
  periodAddBos: any[]        // 时段限制列表
}

interface CinemaFormItem {
  cinemaId: string           // 影院ID
  name: string              // 影院名称
  selectValue: string[]     // 选中的影厅code数组
  halls?: HallItem[]        // 影厅详细信息
}
```

### TicketRestrictionAPI (API接口格式)

```typescript
interface TicketRestrictionAPI {
  couponId?: string
  cinemaRestriction: {
    cinemaScope: number
    cinemas: {
      cinemaId: string
      cinemaName: string
      halls: {
        hallId: string
        hallName: string
      }[]
    }[]
  }
  filmRestriction: {
    filmScope: number
    films: any[]
  }
  periodRestriction: {
    periodScope: number
    periods: any[]
  }
  filmVersionRestriction: {
    versionScope: number
    versions: any[]
  }
}
```

## 依赖组件

- `cinemaSelector`: 影院选择器
- `HallSelector`: 影厅选择器
- `FilmSelector`: 影片选择器
- `filmVersion`: 影片版本选择器
- `WeekTime`: 时段选择器

## 工具函数

组件使用了 `@/utils/couponDataTransform.ts` 中的工具函数进行数据转换：

- `transformFormToAPI`: 将组件格式转换为API格式
- `transformAPIToForm`: 将API格式转换为组件格式
- `validateFormData`: 验证表单数据
- `formatDisplayText`: 格式化显示文本

## 样式特性

- 响应式设计，支持移动端适配
- 使用Element Plus设计规范
- 支持深色模式
- 清晰的视觉层次和间距

## 注意事项

1. 确保所有依赖组件都已正确导入和注册
2. 影厅选择功能需要影院详情API支持
3. 数据转换工具会自动处理格式差异
4. 表单验证会在数据变化时自动触发
5. 组件支持TypeScript，建议使用类型定义

## 更新日志

- v2.0.0: 完全重构，使用TypeScript和Composition API
- v2.0.1: 添加影厅选择功能
- v2.0.2: 优化数据转换和验证逻辑
- v2.0.3: 改进样式和响应式设计
