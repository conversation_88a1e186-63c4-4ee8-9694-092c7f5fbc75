/**
 * 优惠券列表管理 Composable
 * 提供列表数据管理、缓存、分页等功能
 */

import type { CouponItem } from '@/api/modules/coupons/coupon.ts'
import { computed, reactive, ref } from 'vue'
import { changeCouponStatus, getCouponList } from '@/api/modules/coupons/coupon.ts'

// 缓存配置
const CACHE_KEY = 'coupon_list_cache'
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

interface CacheData {
  data: CouponItem[]
  total: number
  timestamp: number
  params: any
}

export function useCouponList() {
  // 响应式状态
  const couponList = ref<CouponItem[]>([])
  const loading = ref(false)
  const total = ref(0)
  const selectedIds = ref<string[]>([])

  // 查询参数
  const queryParams = reactive({
    page: 1,
    size: 10,
    id: undefined,
    name: undefined,
    useOn: '',
    couponType: '',
    validStartTime: undefined,
    validEndTime: undefined,
    useStatus: '',
    generateType: '',
    isAsc: 'desc',
    orderByColumn: 'id',
    channelId: undefined,
  })

  // 计算属性
  const isSingleSelected = computed(() => selectedIds.value.length !== 1)
  const isMultipleSelected = computed(() => selectedIds.value.length === 0)
  const hasData = computed(() => couponList.value.length > 0)

  // 缓存管理
  const getCacheKey = (params: any) => {
    return `${CACHE_KEY}_${JSON.stringify(params)}`
  }

  const getCache = (params: any): CacheData | null => {
    try {
      const cacheKey = getCacheKey(params)
      const cached = localStorage.getItem(cacheKey)
      if (!cached) {
        return null
      }

      const cacheData: CacheData = JSON.parse(cached)
      const now = Date.now()

      // 检查缓存是否过期
      if (now - cacheData.timestamp > CACHE_DURATION) {
        localStorage.removeItem(cacheKey)
        return null
      }

      return cacheData
    }
    catch (error) {
      console.error('获取缓存失败:', error)
      return null
    }
  }

  const setCache = (params: any, data: CouponItem[], total: number) => {
    try {
      const cacheKey = getCacheKey(params)
      const cacheData: CacheData = {
        data,
        total,
        timestamp: Date.now(),
        params: { ...params },
      }
      localStorage.setItem(cacheKey, JSON.stringify(cacheData))
    }
    catch (error) {
      console.error('设置缓存失败:', error)
    }
  }

  const clearCache = () => {
    try {
      const keys = Object.keys(localStorage)
      keys.forEach((key) => {
        if (key.startsWith(CACHE_KEY)) {
          localStorage.removeItem(key)
        }
      })
    }
    catch (error) {
      console.error('清除缓存失败:', error)
    }
  }

  // 转换查询参数类型以匹配API要求
  const convertQueryParams = (params: any) => {
    const converted = { ...params }

    // 转换数字类型字段
    if (converted.id && typeof converted.id === 'string') {
      converted.id = Number.parseInt(converted.id) || undefined
    }
    if (converted.useOn && typeof converted.useOn === 'string') {
      converted.useOn = Number.parseInt(converted.useOn) || undefined
    }
    if (converted.couponType && typeof converted.couponType === 'string') {
      converted.couponType = Number.parseInt(converted.couponType) || undefined
    }
    if (converted.useStatus && typeof converted.useStatus === 'string') {
      converted.useStatus = Number.parseInt(converted.useStatus) || undefined
    }
    if (converted.generateType && typeof converted.generateType === 'string') {
      converted.generateType = Number.parseInt(converted.generateType) || undefined
    }
    if (converted.channelId && typeof converted.channelId === 'string') {
      converted.channelId = Number.parseInt(converted.channelId) || undefined
    }

    // 清理空字符串，转换为undefined
    Object.keys(converted).forEach((key) => {
      if (converted[key] === '') {
        converted[key] = undefined
      }
    })

    return converted
  }

  // 获取优惠券列表
  const fetchCouponList = async (useCache = true): Promise<void> => {
    try {
      loading.value = true

      // 尝试从缓存获取数据
      if (useCache) {
        const cached = getCache(queryParams)
        if (cached) {
          couponList.value = cached.data
          total.value = cached.total
          loading.value = false
          return
        }
      }

      // 转换参数类型
      const convertedParams = convertQueryParams(queryParams)
      const response = await getCouponList(convertedParams)
      console.log('获取优惠券列表响应:', response)

      // API返回的数据结构是 CouponListResponse，需要从 response.data 获取
      const data = response.data

      // 验证分页数据结构
      if (!data || !Array.isArray(data.content)) {
        throw new Error('响应数据格式错误')
      }

      // 直接使用API返回的数据，不进行类型过滤
      couponList.value = data.content || []
      total.value = data.total || 0

      // 缓存数据
      if (useCache) {
        setCache(queryParams, couponList.value, total.value)
      }
    }
    catch (error) {
      console.error('获取优惠券列表失败:', error)
      couponList.value = []
      total.value = 0
      throw error
    }
    finally {
      loading.value = false
    }
  }

  // 刷新列表（不使用缓存）
  const refreshList = () => {
    clearCache()
    return fetchCouponList(false)
  }

  // 搜索
  const handleSearch = () => {
    queryParams.page = 1
    return fetchCouponList(false) // 搜索时不使用缓存
  }

  // 重置搜索
  const handleReset = () => {
    Object.assign(queryParams, {
      page: 1,
      size: 10,
      id: '',
      name: undefined,
      useOn: '',
      couponType: '',
      validStartTime: undefined,
      validEndTime: undefined,
      useStatus: '',
      generateType: '',
      channelId: undefined,
    })
    return fetchCouponList(false)
  }

  // 分页处理
  const handleSizeChange = (size: number) => {
    queryParams.size = size
    queryParams.page = 1
    return fetchCouponList()
  }

  const handleCurrentChange = (page: number) => {
    queryParams.page = page
    return fetchCouponList()
  }

  // 选择处理
  const handleSelectionChange = (selection: CouponItem[]) => {
    selectedIds.value = selection.map(item => item.id)
  }

  // 状态切换
  const handleStatusChange = async (row: CouponItem) => {
    try {
      await changeCouponStatus({
        id: row.id,
        useStatus: row.useStatus,
      })

      // 更新缓存中的数据
      const index = couponList.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        couponList.value[index] = { ...row }
      }

      // 清除相关缓存
      clearCache()

      return true
    }
    catch (error) {
      console.error('状态切换失败:', error)
      throw error
    }
  }

  return {
    // 状态
    couponList,
    loading,
    total,
    selectedIds,
    queryParams,

    // 计算属性
    isSingleSelected,
    isMultipleSelected,
    hasData,

    // 方法
    fetchCouponList,
    refreshList,
    handleSearch,
    handleReset,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange,
    handleStatusChange,
    clearCache,
  }
}
