<!--
 * 作者：zhang
 * 时间：2024年1月7日 14:40:11
 * 功能: 新增和修改优惠券组件
 *  说明：券类型：1.通兑券 2.减至券 3.折扣券 4.满减券
 *
-->

<script setup name="couponCom">
import { getCurrentInstance, onMounted, reactive, ref, useTemplateRef, watch } from 'vue'

// import { stepCouponMainDetail } from '@/api/business/coupon'
import addCounponBase from './addCounponBase.vue'

import addCounponSetting from './addCounponSetting.vue'

const props = defineProps({
  editCoupon: {
    type: Object,
    default: null,
  },
})
const emit = defineEmits()
const { proxy } = getCurrentInstance()

const dialogTitle = ref('创建优惠券')
const pageMode = ref('add')

const activeStep = ref(1)

const addCounponBaseRef = ref(null)

const couponId = ref(null)
function nextSetting(id) {
  // console.log(id);
  couponId.value = id
  activeStep.value = 2
}

// 获取优惠券主信息详情
function getCouponMainValues(couponId) {
  // return stepCouponMainDetail(couponId)
}

if (props.editCoupon) {
  const { mode, data: editCoupon, step } = props.editCoupon
  console.log('2222222222222', props.editCoupon)
  dialogTitle.value = mode === 'edit' ? '编辑优惠券' : '查看优惠券'
  pageMode.value = mode === 'edit' ? 'edit' : 'view'
  activeStep.value = step
  // console.log('props.editCoupon', props.editCoupon);

  if (step === 1) {
    console.log('券基本信息编辑')
    console.log('编辑优惠券', editCoupon)
    // 设置优惠券表单
    const setCouponMainValues = ({ couponCodeCreateConfig, couponMainInfo, couponCanuseConfig, couponSettleMethodConfig }) => {
      console.log({
        couponCodeCreateConfig,
        couponMainInfo,
        couponSettleMethodConfig,
      })

      if (addCounponBaseRef.value) {
        addCounponBaseRef.value.setCouponMainValues(couponMainInfo, couponCanuseConfig, couponCodeCreateConfig, couponSettleMethodConfig)
      }
      else {
        console.error('addCounponBaseRef is not ready')
      }
    }

    // 获取优惠券配置信息
    getCouponMainValues(editCoupon.id).then((res) => {
      const { code, data, msg } = res
      console.log({ code, data, msg })
      if (code !== 200) {
        proxy.$modal.msgError(msg)
      }
      else {
        setCouponMainValues(data)
      }
    })
  }
  else if (step === 2) {
    console.log('券配置信息编辑')
    nextSetting(editCoupon.id)
  }

  onMounted(async () => {
    await nextTick()
    // setCouponMainValues();
  })
}
function close() {
  emit('close')
}

function refresh() {
  emit('refresh')
}

const isFullscreen = ref(false)

function toggleFullscreen() {
  isFullscreen.value = !isFullscreen.value
}

const exhibitionSetting = ref({})
</script>

<template>
  <el-dialog width="80%" :fullscreen="isFullscreen" destroy-on-close @close="close">
    <template #header>
      <el-text size="large">
        {{ dialogTitle }}
      </el-text>
      <el-icon style="float: right;" @click="toggleFullscreen">
        <el-tooltip effect="dark" :content="isFullscreen ? '退出全屏' : '全屏'" placement="top">
          <el-icon :size="20">
            <FullScreen v-if="!isFullscreen" />
            <Crop v-else />
          </el-icon>
        </el-tooltip>
      </el-icon>
    </template>
    <template #default>
      <el-steps style="max-width: 100%;" :active="activeStep" align-center>
        <el-step title="券信息" description="配置券基本信息" @click="activeStep = 1" />
        <el-step title="券设置" description="配置券限制条件和结算方式" @click="activeStep = 2" />
      </el-steps>
      <add-counpon-base
        v-if="activeStep === 1" ref="addCounponBaseRef" :page-mode="pageMode" @refresh="refresh"
        @next-step="nextSetting" @close="close"
      />
      <add-counpon-setting v-if="activeStep === 2" ref="addCounponSettingRef" :coupon-id="couponId" @close="close" />
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
// 去除底部table有白线
:deep(.el-table__inner-wrapper::before) {
  bottom: 0;
  left: 0;
  z-index: 3;
  width: 100%;
  height: 0;
}

.selectshow-group {
  display: flex;
  align-items: center;
  margin-top: 12px;

  .item-label {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 220px;
    height: 72px;
    padding: 0 16px;
    background-color: rgb(0 0 0 / 4%);
    border-radius: 2px;
  }
}
</style>
