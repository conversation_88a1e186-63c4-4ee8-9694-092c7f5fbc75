<script setup>
import { defineEmits, defineProps } from 'vue'
import { getGoodsTypeAll } from '@/api/business/goodsType'
import cinemaGoodsSelect from '@/components/cinemaGoodsSelect'

import cinemaSelector from '@/components/cinemaSelector/index.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: {
      cinemaScope: 0,
      cinemaIds: [],
      goodsSelectScope: 1,
      goodsTypeScope: 0,
      goodsTypeIds: [],
      goodsInfoScope: 0,
      cinemaGoodsInfoList: [],
    },
  },
})

// Emit
const emit = defineEmits(['update:modelValue'])

// Methods
function updateGoods(selectedGoods) {
  console.log({ selectedGoods })
  const newSelectedGoods = selectedGoods.map((item) => {
    return {
      ...item,
      goodsInfoId: item.goodsInfoId ? item.goodsInfoId : item.goodsId,
    }
  })
  emit('update:modelValue', { ...props.modelValue, cinemaGoodsInfoList: newSelectedGoods })
}

function groupByCinemaName(goodsList) {
  return goodsList.reduce((acc, good) => {
    (acc[good.cinemaName] = acc[good.cinemaName] || []).push(good)
    return acc
  }, {})
}

// 所有卖品分类
const goodTypes = ref([])
/**
 * 获取卖品分类
 * @returns {Promise<void>}
 */
async function getGoodTypes() {
  const res = await getGoodsTypeAll()
  if (res.code === 200) {
    goodTypes.value = res.data
  }
}
getGoodTypes()

// 完成影城选择
function cinemaComplete(cinemas) {
  props.modelValue.cinemaIds = cinemas.map((item) => {
    let selectValue = []
    if (item.halls) {
      selectValue = item.halls.map(hall => hall.hallId)
    }
    return {
      id: item.id,
      cinemaId: item.id,
      name: item.name,
      selectValue,
    }
  })

  // 显示影厅
}
function removeCinema(item) {
  emit('update:modelValue', {
    ...props.modelValue,
    cinemaIds: props.modelValue.cinemaIds.filter(c => c.id !== item.id),
  })
}
</script>

<template>
  <el-form-item label="限制影院方式">
    <el-radio-group v-model="modelValue.cinemaScope">
      <el-radio :value="0">
        {{ _codeMap[0] }}
      </el-radio>
      <el-radio :value="1">
        {{ _codeMap[1] }}
      </el-radio>
      <el-radio :value="2">
        {{ _codeMap[2] }}
      </el-radio>
    </el-radio-group>
  </el-form-item>
  <el-form-item v-if="modelValue.cinemaScope > 0" label="限制影院">
    <!-- {{ couponForm.cinemaIds }} -->
    <cinema-selector :model-value="modelValue.cinemaIds" @on-complete="cinemaComplete" />
    <el-text v-if="modelValue.cinemaScope === 1" style="margin-left: 1vw;">
      已选择{{ modelValue.cinemaIds.length }}家影院
    </el-text>
    <el-text v-if="modelValue.cinemaScope === 2" style="margin-left: 1vw;">
      已排除{{ modelValue.cinemaIds.length }}家影院
    </el-text>
    <div style="width: 100%; margin-top: 10px;">
      <el-tag
        v-for="item in modelValue.cinemaIds" :key="item" closable
        :type="modelValue.cinemaScope === 2 ? 'danger' : 'primary'" style="margin-right: 8px;"
        @close="removeCinema(item)"
      >
        {{ item.name }}
      </el-tag>
    </div>
  </el-form-item>

  <el-form-item label="适用商品类型" size="large">
    <el-radio-group v-model="modelValue.goodsSelectScope">
      <el-radio :value="0">
        不限
      </el-radio>
      <el-radio :value="1">
        指定分类
      </el-radio>
      <el-radio :value="2">
        指定商品
      </el-radio>
    </el-radio-group>
  </el-form-item>
  <el-form-item v-if="modelValue.goodsSelectScope == 1" label="指定商品分类">
    <el-radio-group v-model="modelValue.goodsTypeScope">
      <el-radio :value="0">
        不限
      </el-radio>
      <el-radio :value="1">
        指定卖品分类可用
      </el-radio>
      <el-radio :value="2">
        指定卖品分类不可用
      </el-radio>
    </el-radio-group>
  </el-form-item>
  <el-form-item v-if="modelValue.goodsSelectScope == 1 && modelValue.goodsTypeScope > 0" label="选择卖品分类">
    <!-- {{ goodTypes }} -->
    <el-select v-model="modelValue.goodsTypeIds" multiple placeholder="请选择分类">
      <el-option v-for="item in goodTypes" :key="item.id" :label="item.typeName" :value="item.id" />
    </el-select>
  </el-form-item>
  <el-form-item v-if="modelValue.goodsSelectScope == 2" label="指定商品">
    <el-radio-group v-model="modelValue.goodsInfoScope">
      <el-radio :value="0">
        不限
      </el-radio>
      <el-radio :value="1">
        指定卖品可用
      </el-radio>
      <el-radio :value="2">
        指定卖品不可用
      </el-radio>
    </el-radio-group>
  </el-form-item>
  <el-form-item v-if="modelValue.goodsSelectScope == 2 && modelValue.goodsInfoScope > 0" label="选择卖品">
    <el-row>
      <el-col v-if="modelValue.cinemaGoodsInfoList.length" :span="24">
        <div>
          <el-text type="info">
            已选择{{ modelValue.cinemaGoodsInfoList.length }}个卖品
          </el-text>
          <el-card shadow="never">
            <el-row
              v-for="(item, key, index) in groupByCinemaName(modelValue.cinemaGoodsInfoList)"
              :key="index" style="margin-top: 20px;border-bottom: 1px solid #eee;"
            >
              <el-col :span="6">
                {{ key }}
              </el-col>
              <el-col :span="18">
                <el-tag
                  v-for="good in item" :key="good.goodsId" type="success" effect="dark"
                  style="margin-left: 1vw;"
                >
                  {{ good.goodsName }}
                </el-tag>
              </el-col>
            </el-row>
          </el-card>
        </div>
      </el-col>
      <el-col :span="24">
        <cinema-goods-select v-model="modelValue.cinemaGoodsInfoList" :dialog-visible="true" @submit="updateGoods">
          <template #default="scope">
            <el-button type="primary">
              选择卖品
            </el-button>
          </template>
        </cinema-goods-select>
      </el-col>
    </el-row>
  </el-form-item>
</template>

<style scoped>
/* Add any specific styles for the component here */
</style>
