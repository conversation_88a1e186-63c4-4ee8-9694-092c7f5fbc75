<template>
  <div>
    <el-divider content-position="left" style="margin: 48px 0">
      <span>生成方式</span>
    </el-divider>

    <el-form-item label="生成方式">
      <template #label>
        <div style="display: flex; align-items: center">
          <span>生成方式</span>
          <el-tooltip class="item" effect="dark" content="券新建完成后不可更改，请谨慎选择" placement="top">
            <el-icon>
              <QuestionFilled color="#999" />
            </el-icon>
          </el-tooltip>
        </div>
      </template>
      <el-radio-group v-model="modelValue.type" >
        <el-tooltip class="item" effect="dark" content="适用场景：一次性卖给某个大客户多少张券" placement="top">
          <el-radio :value="1">一次性生成</el-radio>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="适用场景：用户通过线上营销活动获得优惠券" placement="top">
          <el-radio :value="0">需要时生成</el-radio>
        </el-tooltip>
      </el-radio-group>
    </el-form-item>
    <el-form-item :label="modelValue.type === 1 ? '生成数量' : '生成张数上限'">
      <el-input-number v-model="modelValue.num" :min="1" :max="10000" :step="1" controls-position="right"
        placeholder="请输入生成数量"></el-input-number>
    </el-form-item>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue';

// Props
const props = defineProps({
  // type: Number, // 生成方式 0.一次生成N张 1.按需生成
  // num: Number, // 生成数量 当generateType=1时生效 
  // isEditMode: Boolean
  modelValue: {
    type: Object,
    required: true
  }
});

// Emit
const emit = defineEmits(['update:modelValue']);


// Watch for changes and emit updates
watch(() => props.modelValue, (newVal) => {
  emit('update:modelValue', newVal);
}, { deep: true });
</script>

<style scoped>
/* Add any specific styles for the component here */
</style>
