/**
 * 优惠券相关常量配置
 * 该文件定义了优惠券系统中使用的所有枚举类型、常量和配置项
 */

// ===========================================
// 枚举定义 - 用于表示状态和类型
// ===========================================

/**
 * 优惠券适用类型枚举
 * 定义券可以使用的商品类型
 */
export enum USE_ON_TYPES {
  /** 电影票 */
  MOVIE_TICKET = 0,
  /** 卖品 */
  GOODS = 1,
  /** 演出票 */
  SHOW_TICKET = 2,
  /** 展览票 */
  EXHIBITION_TICKET = 3,
}

/**
 * 优惠券类型枚举
 * 定义不同的优惠券优惠方式
 * 0-满减，1-减至，2-通兑，3-折扣，4-多对一
 */
export enum COUPON_TYPES {
  /** 满减券 - 满X减Y */
  FULL_REDUCTION = 0,
  /** 减至券 - 减至指定金额 */
  REDUCE_TO = 1,
  /** 通兑券 - 可兑换不同商品 */
  EXCHANGE = 2,
  /** 折扣券 - 打折优惠 */
  DISCOUNT = 3,
  /** 多兑一券 - 多张券换一张 */
  MULTI_EXCHANGE = 4,
}

/**
 * 渠道范围枚举
 * 定义券的适用渠道范围
 */
export enum CHANNEL_SCOPE {
  /** 全部渠道 */
  ALL = 0,
  /** 部分渠道 */
  PARTIAL = 1,
}

/**
 * 服务费减免枚举
 * 定义是否减免服务费
 */
export enum SERVICE_FEE_REDUCTION {
  /** 减免服务费 */
  REDUCE = 0,
  /** 不减免服务费 */
  NOT_REDUCE = 1,
}

/**
 * 有效期方式枚举
 * 定义券的有效期设置方式
 */
export enum VALIDITY_SCOPE {
  /** 指定日期可用 */
  SPECIFIC_DATE = 0,
  /** 绑定后N天可用 */
  DAYS_AFTER_BIND = 1,
}

/**
 * 生成方式枚举
 * 定义券的生成策略
 */
export enum GENERATE_TYPE {
  /** 按需生成 */
  ON_DEMAND = 0,
  /** 一次生成N张 */
  BATCH = 1,
}

/**
 * 结算方式枚举
 * 定义券的结算策略
 */
export enum SETTLE_METHOD {
  /** 无需结算 */
  NO_SETTLE = 0,
  /** 需要结算 */
  NEED_SETTLE = 1,
}

/**
 * 价格类型枚举
 * 定义结算价格的计算方式
 */
export enum PRICED_TYPE {
  /** 固定票价 */
  FIXED_PRICE = 0,
  /** 最低票价 */
  LOWEST_PRICE = 1,
}

/**
 * 通兑券补差类型枚举
 * 定义通兑券的补差方式
 */
export enum EXCHANGE_DIFF_TYPE {
  /** 优惠金额补差 */
  AMOUNT_DIFF = 0,
  /** 按最低票价抵扣 */
  LOWEST_PRICE_DEDUCT = 1,
}

// ===========================================
// 类型定义 - 用于增强类型安全性
// ===========================================

/**
 * 选项项类型定义
 */
export interface OptionItem {
  /** 值 */
  value: number
  /** 标签 */
  label: string
}

/**
 * 券类型选项项类型定义
 */
export interface CouponTypeOption extends OptionItem {
  /** 描述信息 */
  des: {
    /** 文本 */
    text: COUPON_TYPE_SCOPE
    /** 提示 */
    tip: string
    /** 类型 */
    type: 'primary' | 'success' | 'info' | 'warning' | 'danger'
  }
}

// ===========================================
// 静态配置 - 用于页面显示和验证
// ===========================================

/**
 * 适用类型选项配置
 * 用于页面下拉选择和显示
 */
export const USE_ON_OPTIONS: OptionItem[] = [
  { value: USE_ON_TYPES.MOVIE_TICKET, label: '电影票' },
  { value: USE_ON_TYPES.GOODS, label: '卖品' },
  { value: USE_ON_TYPES.SHOW_TICKET, label: '演出票' },
  { value: USE_ON_TYPES.EXHIBITION_TICKET, label: '展览票' },
] as const

// 券所属类型 订单 单品
export enum COUPON_TYPE_SCOPE {
  ORDER = '订单',
  ITEM = '单品',
}

/**
 * 券类型选项配置
 * 用于页面券类型选择和显示
 */
export const COUPON_TYPE_OPTIONS: CouponTypeOption[] = [
  {
    value: COUPON_TYPES.FULL_REDUCTION,
    label: '满减券',
    des: { text: COUPON_TYPE_SCOPE.ORDER, tip: '针对整笔订单进行满减', type: 'primary' },
  },
  {
    value: COUPON_TYPES.REDUCE_TO,
    label: '减至券',
    des: { text: COUPON_TYPE_SCOPE.ITEM, tip: '单品减至指定金额', type: 'success' },
  },
  {
    value: COUPON_TYPES.EXCHANGE,
    label: '通兑券',
    des: { text: COUPON_TYPE_SCOPE.ITEM, tip: '可兑换不同价位商品', type: 'success' },
  },
  {
    value: COUPON_TYPES.DISCOUNT,
    label: '折扣券',
    des: { text: COUPON_TYPE_SCOPE.ORDER, tip: '整笔订单打折', type: 'primary' },
  },
  {
    value: COUPON_TYPES.MULTI_EXCHANGE,
    label: '多兑一券',
    des: { text: COUPON_TYPE_SCOPE.ITEM, tip: '多张券兑换一张', type: 'success' },
  },
] as const

/**
 * 渠道范围选项配置
 */
export const CHANNEL_SCOPE_OPTIONS: OptionItem[] = [
  { value: CHANNEL_SCOPE.ALL, label: '全部' },
  { value: CHANNEL_SCOPE.PARTIAL, label: '部分' },
] as const

/**
 * 服务费减免选项配置
 */
export const SERVICE_FEE_OPTIONS: OptionItem[] = [
  { value: SERVICE_FEE_REDUCTION.REDUCE, label: '减免' },
  { value: SERVICE_FEE_REDUCTION.NOT_REDUCE, label: '不减免' },
] as const

// ===========================================
// 业务常量 - 用于业务逻辑计算
// ===========================================

/**
 * 数据转换比例常量
 * 用于金额单位转换
 */
export const CONVERSION_RATE = {
  /** 元转分的比例 */
  YUAN_TO_FEN: 100,
  /** 分转元的比例 */
  FEN_TO_YUAN: 0.01,
} as const

/**
 * 默认值常量
 * 用于表单初始化和默认设置
 */
export const DEFAULT_VALUES = {
  /** 默认服务费（元） */
  USER_SERVICE_FEE: 3,
  /** 默认过期天数 */
  OVERDUE_DAY: 1,
  /** 默认多兑一规则数量 */
  MULTI_EXCHANGE_RULES: 3,
  /** 默认券生成方式 */
  GENERATE_TYPE: GENERATE_TYPE.BATCH,
  /** 默认结算方式 */
  SETTLE_METHOD: SETTLE_METHOD.NO_SETTLE,
  /** 默认价格类型 */
  PRICED_TYPE: PRICED_TYPE.FIXED_PRICE,
} as const

/**
 * 输入限制常量
 * 用于表单验证和输入控制
 */
export const INPUT_LIMITS = {
  /** 券名称最大长度 */
  NAME_MAX_LENGTH: 50,
  /** 其他说明最大长度 */
  MEMO_MAX_LENGTH: 300,
  /** 备注最大长度 */
  REMARK_MAX_LENGTH: 300,
  /** 折扣最大值 */
  DISCOUNT_MAX: 10,
  /** 金额最大值 */
  AMOUNT_MAX: 99999999,
  /** 服务费最大值 */
  SERVICE_FEE_MAX: 999,
  /** 面值最大值 */
  VALUABLE_MAX: 999999,
  /** 多兑一规则最大数量 */
  MULTI_EXCHANGE_MAX: 10,
  /** 减至券最大金额 */
  REDUCE_TO_MAX: 999,
} as const

/**
 * 业务规则常量
 * 用于业务逻辑判断
 */
export const BUSINESS_RULES = {
  /** 支持多兑一券的适用类型 */
  MULTI_EXCHANGE_SUPPORTED_TYPES: [USE_ON_TYPES.MOVIE_TICKET] as USE_ON_TYPES[],
  /** 需要服务费的适用类型 */
  SERVICE_FEE_REQUIRED_TYPES: [USE_ON_TYPES.MOVIE_TICKET] as USE_ON_TYPES[],
  /** 通兑券支持的补差类型 */
  EXCHANGE_DIFF_TYPES: [EXCHANGE_DIFF_TYPE.AMOUNT_DIFF, EXCHANGE_DIFF_TYPE.LOWEST_PRICE_DEDUCT] as EXCHANGE_DIFF_TYPE[],
  /** 支持按最低票价抵扣的适用类型（仅电影票） */
  LOWEST_PRICE_DEDUCT_SUPPORTED_TYPES: [USE_ON_TYPES.MOVIE_TICKET] as USE_ON_TYPES[],
} as const

// ===========================================
// 工具函数 - 用于常量查找和转换
// ===========================================

/**
 * 根据值查找对应的选项标签
 * @param options 选项数组
 * @param value 要查找的值
 * @returns 对应的标签，未找到返回空字符串
 */
export function findOptionLabel(options: OptionItem[], value: number): string {
  return options.find(option => option.value === value)?.label || ''
}

/**
 * 根据适用类型值获取标签
 * @param value 适用类型值
 * @returns 对应的标签
 */
export function getUseOnLabel(value: USE_ON_TYPES): string {
  return findOptionLabel(USE_ON_OPTIONS, value)
}

/**
 * 根据券类型值获取选项
 * @param value 券类型值
 * @returns 对应的选项对象
 */
export function getCouponTypeOption(value: COUPON_TYPES): CouponTypeOption | undefined {
  return COUPON_TYPE_OPTIONS.find(option => option.value === value)
}

/**
 * 检查是否支持多兑一券
 * @param useOnType 适用类型
 * @returns 是否支持
 */
export function isMultiExchangeSupported(useOnType: USE_ON_TYPES): boolean {
  return BUSINESS_RULES.MULTI_EXCHANGE_SUPPORTED_TYPES.includes(useOnType)
}

/**
 * 检查是否需要服务费设置
 * @param useOnType 适用类型
 * @returns 是否需要服务费设置
 */
export function isServiceFeeRequired(useOnType: USE_ON_TYPES): boolean {
  return BUSINESS_RULES.SERVICE_FEE_REQUIRED_TYPES.includes(useOnType)
}

/**
 * 检查是否支持按最低票价抵扣
 * @param useOnType 适用类型
 * @returns 是否支持按最低票价抵扣
 */
export function isLowestPriceDeductSupported(useOnType: USE_ON_TYPES): boolean {
  return BUSINESS_RULES.LOWEST_PRICE_DEDUCT_SUPPORTED_TYPES.includes(useOnType)
}
