<template>
    <el-dialog
        title="选择企业用户"
        append-to-body
        align-center
        width="1250px"
        destroy-on-close>
        <el-row :gutter="10">
          <el-col :span="5">
            <el-card shadow="never">
              <div>
                <el-input
                v-model="searchName"
                placeholder="输入企业名称"
                clearable />
              </div>
              <div class="company-list">
                <ul>
                  <li
                    v-for="item in filterCompany"
                    :class="{ active: item.id === selectedCompnay.id }"
                    @click="handleChangeCinema(item)"
                  >
                    <el-text style="margin-right: 10px" size="small">{{item.compName}}</el-text>
                    <el-text size="small" type="info">{{ item.cityName }}</el-text>
                  </li>
                </ul>
              </div>
            </el-card>
          </el-col>
          <el-col :span="19">
            <el-card shadow="never">
              <el-form :model="queryParams" ref="queryRef" :inline="true">
                <el-form-item label="会员账号" prop="userName">
                  <el-input v-model="queryParams.userName" placeholder="请输入" clearable style="width: 200px" />
                </el-form-item>
                <el-form-item label="手机号" prop="phonenumber">
                  <el-input v-model="queryParams.phonenumber" placeholder="请输入" clearable style="width: 200px" />
                </el-form-item>
                <el-form-item label="创建时间" style="width: 308px">
                    <el-date-picker
                      v-model="dateRange"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      type="daterange"
                      range-separator="-"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                    />
                </el-form-item>
                <el-form-item label="">
                  <el-button type="primary" icon="search" @click="handleQuery">查询</el-button>
                </el-form-item>
              </el-form>
              <el-table ref="userListTableRef" v-loading="tableLoading" stripe :data="dataList">
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column label="会员ID" align="left" prop="userId" width="190" />
                <el-table-column label="会员账号" align="left" prop="userName" />
                <el-table-column label="会员昵称" align="left" prop="nickName" />
                <el-table-column label="手机号" align="left" prop="phonenumber" />
                <el-table-column label="注册时间" align="left" prop="createTime" width="160" />
              </el-table>
              <div style="display: flex;align-items: center;">
                <div style="display: flex;align-items: center;margin-top: 16px;margin-right: 16px;">
                  <el-checkbox v-model="isSelectAll" label="全选" @change="handleSelectAll" />
                  <span style="margin-left: 8px;">选中{{selectUserNum}}个</span>
                </div>
                <pagination
                  v-show="total > 0"
                  :total="total"
                  v-model:page="queryParams.pageNum"
                  v-model:limit="queryParams.pageSize"
                  @pagination="getList" />
              </div>
            </el-card>
          </el-col>
        </el-row>
        <template #footer>
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确定</el-button>
        </template>
    </el-dialog>
</template>

<script setup name="selectCompanyUserComp">
import { getCurrentInstance, onMounted, reactive, ref, watch } from "vue"
const { proxy } = getCurrentInstance()
import { getCompanyAll, getCompanyUserList, getCompanyListAll } from "@/api/company/index.js"

const emit = defineEmits()
const props = defineProps({
})

const companyList = ref([])
const filterCompany = ref([])
const selectedCompnay = ref({})
const dateRange = ref([]);
const queryParams = ref({
  pageSize: 10,
  pageNum: 1,
  compId: null,
  userName: '',
  phonenumber: '',
})

onMounted(() => {
  proxy.$modal.loading('加载中...')
  getCompanyAll().then(res => {
    proxy.$modal.closeLoading()
    companyList.value = res.data || []
    filterCompany.value = res.data || []

    if (companyList.value.length > 0) {
      selectedCompnay.value = companyList.value[0]
      queryParams.value.compId = companyList.value[0].id
      getList()
    }
  })
})
const searchName = ref('')
watch(searchName, (val) => {
  if (val) {
    filterCompany.value = companyList.value.filter(item => item.compName.includes(val))
  } else {
    filterCompany.value = companyList.value
  }
})

const handleChangeCinema = (data) => {
  selectedCompnay.value = data;
  queryParams.value.compId = data.id;
  tableLoading.value = true;
  isSelectAll.value = false;
  selectUserNum.value = 0
  temUserIds.value = []
  getList();
};

const tableLoading = ref(true)
let dataList = ref([])
const total = ref(0)
const getList = () => {
  getCompanyUserList(proxy.addDateRange(queryParams.value, dateRange.value)).then(res => {
    dataList.value = res.rows || []
    total.value = res.total;
    tableLoading.value = false;
  })
};

const handleQuery = () => {
  queryParams.value.pageNum = 1;
  tableLoading.value = true;
  getList()
};

const isSelectAll = ref(false)
const selectUserNum = ref(0)
const userListTableRef = ref(null)
const temUserIds = ref([])
const handleSelectAll = () => {
  if (isSelectAll.value) {
    let baseParams = { ...queryParams.value }
    const { pageNum, pageSize, ...baseParamsWithoutPage } = baseParams
    baseParams = baseParamsWithoutPage
    baseParams = proxy.addDateRange(baseParams, dateRange.value)
    getCompanyListAll(baseParams).then(res => {
      selectUserNum.value = res.data.length
      temUserIds.value = res.data.map(item => item.userId)
    })
  } else {
    selectUserNum.value = 0
    temUserIds.value = []
  }
}

const handleSubmit = () => {
  const data = {
    compId: selectedCompnay.value.id,
    compName: selectedCompnay.value.compName
  }
  const selectRows = userListTableRef.value.getSelectionRows()
  if (isSelectAll.value) {
    if (temUserIds.value.length === 0) {
      proxy.$message.error('请选择用户')
      return
    }
  } else {
    if (!selectRows.length) {
      proxy.$message.error('请选择用户');
      return
    }
  }
  if (isSelectAll.value) {
    data.userIds = temUserIds.value
  } else {
    data.userIds = selectRows.map(item => item.userId)
  }
  emit('submit', data)
}

const handleCancel = () => {
  emit('close')
}
</script>

<style lang="scss" scoped>
.company-list {
  max-height: calc(100vh - 210px);
  overflow: auto;
  margin-top: 12px;

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      padding: 10px 5px 10px 0;
      border-bottom: 1px solid #eee;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 5;

      &:hover {
        background-color: #eee;
      }

      span {
        span:nth-child(1) {
          flex: 4;
        }
      }

      &.active {
        color: #007fff;
        transition: ease-in-out all 0.3s;

        span:nth-child(1) {
          flex: 4;
        }

        &:before {
          content: "";
          display: inline-block;
          position: relative;
          left: 0;
          top: 0;
          width: 5px;
          height: 20px;
          background: #007fff;
          margin-right: 10px;
          flex: 0.1;
        }
      }
    }

  }
}
</style>