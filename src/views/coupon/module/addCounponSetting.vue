<script setup name="addCounponSetting">
import { onMounted, ref } from 'vue'
import {
  stepCouponConfigUpdate,
  stepCouponInfoDetail,
} from '@/api/modules/coupons/coupon.ts'
import FaModal from '@/ui/components/FaModal/index.vue' // 电影券
import addCounponSettingExhibition from './addCounponSetting_Exhibition.vue' // 展览券
import addCounponSettingGoods from './addCounponSetting_Goods.vue' // 适用卖品条件限制
import addCounponSettingShow from './addCounponSetting_Show.vue' // 适用演出条件场次
import addCounponSettingTicket from './addCounponSetting_Ticket.vue'

const props = defineProps({
  couponId: {
    // 优惠券id
    type: String,
    required: true,
  },
})
const { proxy } = getCurrentInstance()
const useOn = ref(null) // 适用类型 0,电影票 1,卖品 2.演出

/**
 * 优惠券主要信息
 */
const mainInfo = ref({})
/**
 * 电影券配置项
 */
const ticketConfigs = ref([])
/**
 * 演出券配置项
 */
const showConfigs = ref([])
/**
 * 卖品券配置项
 */
const goodsConfigs = ref([])
/**
 * 展览券配置项
 */
const exhibitionConfigs = ref({})

// 初始化影票数据结构化
function initializeTicketConfigs(couponTicketConfigs) {
  ticketConfigs.value = couponTicketConfigs.map((ticketConfig) => {
    if (ticketConfig.couponTicketSelectScopeConfig !== null) {
      return {
        cinemaScope: ticketConfig.couponTicketSelectScopeConfig.cinemaScope,
        cinemaIds: ticketConfig.couponCinemas.map(item => ({
          ...item,
          id: item.cinemaId,
          name: item.cinemaName,
          selectValue: item.hallIds,
        })),
        filmScope: ticketConfig.couponTicketSelectScopeConfig.filmScope,
        filmIds: ticketConfig.couponFilms.map(film => ({
          id: film.filmId,
          name: film.filmName,
          code: film.filmCode,
        })),
        periodAddBos: ticketConfig.couponPeriods.map(period => ({
          week: period.dayTimes.days,
          time: [
            `1999-01-01 ${period.dayTimes.start}`,
            `1999-01-01 ${period.dayTimes.end}`,
          ],
        })),
        filmVersions: ticketConfig.couponFilmVersions.map(
          item => item.filmVersion,
        ),
      }
    }
    else {
      return {
        cinemaScope: 0,
        cinemaIds: [],
        filmIds: [],
        filmScope: 0,
        lowestPrice: 0,
        filmVersions: [],
        periodAddBos: [],
      }
    }
  })
}
// 初始化卖品数据结构化
function initializeGoodsConfigs(goodsConfig) {
  console.log('goodsConfig:', goodsConfig)

  goodSetting.value
    = goodsConfig.couponGoodsSelectScopeConfig !== null
      ? {
          cinemaScope: goodsConfig.couponGoodsSelectScopeConfig.cinemaScope,
          goodsSelectScope:
            goodsConfig.couponGoodsSelectScopeConfig.goodsSelectScope,
          goodsTypeScope:
            goodsConfig.couponGoodsSelectScopeConfig.goodsSelectScope === 1
              ? goodsConfig.couponGoodsSelectScopeConfig.goodsTypeScope
              : 0,
          goodsTypeIds:
            goodsConfig.couponGoodsSelectScopeConfig.goodsSelectScope === 0
              ? []
              : goodsConfig.couponGoodsTypes.map(item => item.goodsTypeId),
          goodsInfoScope:
            goodsConfig.couponGoodsSelectScopeConfig.goodsSelectScope === 2
              ? goodsConfig.couponGoodsSelectScopeConfig.goodsInfoScope
              : 0,
          cinemaIds: goodsConfig.couponCinemas.map(item => ({
            ...item,
            id: item.cinemaId,
            name: item.cinemaName,
            selectValue: item.hallIds,
          })),
          cinemaGoodsInfoList:
            goodsConfig.couponGoodsSelectScopeConfig.goodsSelectScope > 0
              ? goodsConfig.couponGoodsInfos
              : [],
        }
      : {
          ruleId: '',
          cinemaScope: 0,
          cinemaIds: [],
          goodsSelectScope: 1,
          goodsTypeScope: 0,
          goodsTypeIds: [],
          goodsInfoScope: 0,
          cinemaGoodsInfoList: [],
        }
}
// 初始化展览数据结构化
function initializeExhibitionConfigs(_exhibitionConfig) {
  const {
    couponExhibitionSelectScopeConfig,
    couponExhibitionFilms,
    couponFilms,
  } = _exhibitionConfig
  console.log({
    couponExhibitionSelectScopeConfig,
    couponExhibitionFilms,
    couponFilms,
  })
  if (couponExhibitionSelectScopeConfig !== null) {
    console.log(
      couponExhibitionSelectScopeConfig.cinemaScope,
      couponExhibitionSelectScopeConfig.filmScope,
    )
    const hallMap = new Map()
    let uniqueCinemaIds = []
    if (couponExhibitionSelectScopeConfig.cinemaScope) {
      _exhibitionConfig.couponCinemas?.forEach((cinema) => {
        if (!hallMap.has(cinema.cinemaId)) {
          hallMap.set(cinema.cinemaId, {
            cinemaId: cinema.cinemaId,
            halls: [],
          })
        }
        if (cinema.hallId) {
          if (!hallMap.get(cinema.cinemaId).halls.includes(cinema.hallId)) {
            hallMap.get(cinema.cinemaId).halls.push(cinema.hallId)
          }
        }
      })
      uniqueCinemaIds = [
        ...new Set(
          _exhibitionConfig.couponCinemas.map(item => item.cinemaId),
        ),
      ]
    }

    // console.log({uniqueCinemaIds})
    exhibitionConfigs.value = {
      exhibitionFilmScope: couponExhibitionSelectScopeConfig.filmScope,
      exhibitionFilmIds: couponFilms.map(item => item.filmId),
      exhibitionCinemaScope: couponExhibitionSelectScopeConfig.cinemaScope,
      exhibitionCinemaIds: uniqueCinemaIds || [],
      exhibitionCinemaHallIds: hallMap.size ? Array.from(hallMap.values()) : [],
    }
  }
  else {
    exhibitionConfigs.value = {
      exhibitionFilmScope: 0,
      exhibitionFilmIds: [],
      exhibitionCinemaScope: 0,
      exhibitionCinemaIds: [],
      exhibitionCinemaHallIds: [],
    }
  }
  console.log('exhibitionConfigs:', exhibitionConfigs.value)
}

onMounted(() => {
  proxy.$modal.loading('加载中...')
  stepCouponInfoDetail(props.couponId)
    .then((res) => {
      const { code, data, msg } = res
      if (code === 200) {
        const {
          couponMainInfo,
          couponTicketConfigs,
          couponShowConfigs,
          couponGoodsConfigs,
          couponCanuseConfig,
          couponSettleMethodConfig,
          couponCodeCreateConfig,
          couponExhibitionConfigs,
        } = data
        mainInfo.value = couponMainInfo
        useOn.value = mainInfo.value.useOn

        // 校验 useOn 的合法性
        if (![0, 1, 2, 3].includes(useOn.value)) {
          FaModal()
          proxy.$modal.msgError('券类型无效，请检查数据！')
          proxy.$modal.closeLoading()
          return
        }

        switch (useOn.value) {
          case 0: // 电影券
            initializeTicketConfigs(couponTicketConfigs)
            break
          case 1: // 卖品券
            const goodsConfigs = couponGoodsConfigs[0]

            initializeGoodsConfigs(goodsConfigs)
            break
          case 2: // 演出券
            showConfigs.value = couponShowConfigs
            break
          case 3: // 展览券
            const _config = couponExhibitionConfigs[0]
            initializeExhibitionConfigs(_config)
            break
          default:
            break
        }
      }
      else {
        proxy.$modal.msgError(msg)
      }
      proxy.$modal.closeLoading()
    })
    .catch((err) => {
      proxy.$modal.closeLoading()
    })
})

// 适用演出条件
const showSetting = ref({
  showScope: 0,
  showScheduleIds: [],
  showPopup: {
    show: false,
    showIds: [],
    cinemaIds: [],
  },
  showScheduleList: [],
})

// 适用卖品条件限制
const goodSetting = ref({
  ruleId: '',
  cinemaScope: 0,
  cinemaIds: [],
  goodsSelectScope: 1,
  goodsTypeScope: 0,
  goodsTypeIds: [],
  goodsInfoScope: 0,
  cinemaGoodsInfoList: [],
})

// 保存-影票 配置
function saveTicketConfigs() {
  try {
    console.log({ ticketConfigs })
    const config = []

    ticketConfigs.value.forEach((item, index) => {
      const { id: ruleId } = mainInfo.value?.couponRuleExchanges[index] || null
      const _ticketConfig = ticketConfigs.value[index] // 电影券配置
      console.log('AAAAAAAAAAAA', _ticketConfig)
      const couponTicketSelectScopeConfig = {
        // 电影券限制条件
        ruleId,
        cinemaScope: _ticketConfig.cinemaScope, // 限制影院方式：0 不限 1 指定影院 2 排除选择的影院
        // cinemaIds: _ticketConfig.cinemaScope>0 ? _ticketConfig.cinemaIds.map(item => item.cinemaId):[],
        filmScope: _ticketConfig.filmScope, // 适用影片类型 0.不限 1.指定影片 2.指定最低限价影片
        filmVersionScope: _ticketConfig.filmVersions.length > 0 ? 1 : 0, // 适用影片版本 0.不限 1.指定影片版本
        periodScope: _ticketConfig.periodAddBos?.length > 0 ? 1 : 0 || 0, // 适用时段：全部时段0，指定时段1
        lowestPrice:
          _ticketConfig.filmScope === 2 ? _ticketConfig.lowestPrice : null, // 适用最低限价
      }
      // console.log({ couponTicketSelectScopeConfig });
      const couponCinemas = _ticketConfig.cinemaIds.flatMap((cinema) => {
        console.log({ cinema })
        const list = []
        if (cinema.selectValue.length > 0) {
          cinema.selectValue.forEach((value) => {
            list.push({
              ruleId,
              couponId: mainInfo.value.id,
              cinemaId: cinema.cinemaId,
              cinemaName: cinema.name,
              hallId: value,
            })
          })
        }
        else {
          console.log('yyyyy', cinema)
          list.push({
            ruleId,
            couponId: mainInfo.value.id,
            cinemaId: cinema.cinemaId,
            cinemaName: cinema.name,
          })
        }
        return list
      })

      const couponPeriods = _ticketConfig.periodAddBos.flatMap(
        (period, index) => {
          return period.week.map((day) => {
            return {
              ruleId,
              num: index, // Assuming 'num' is a list of numbers, using the first one as 'num'
              day,
              start: new Date(period.time[0]).toLocaleTimeString('en-GB', {
                hour12: false,
              }), // Convert to "HH:mm:ss"
              end: new Date(period.time[1]).toLocaleTimeString('en-GB', {
                hour12: false,
              }), // Convert to "HH:mm:ss"
            }
          })
        },
      )
      // console.log('couponPeriods:', couponPeriods);
      // console.log(_ticketConfig);

      const couponFilms = _ticketConfig.filmIds.flatMap((film) => {
        return {
          ruleId,
          filmId: film.id,
          filmName: film.name,
          filmCode: film.code,
        }
      })
      // console.log('couponFilms:', couponFilms);

      const couponFilmVersions = _ticketConfig.filmVersions.flatMap(
        (filmVersion) => {
          return {
            ruleId,
            filmVersion,
          }
        },
      )

      // console.log('couponFilmVersions:', couponFilmVersions);

      config.push({
        couponTicketSelectScopeConfig,
        couponCinemas,
        couponPeriods,
        couponFilms,
        couponFilmVersions,
      })
    })
    console.log('config:', config)
    return config
  }
  catch (error) {
    console.error('Error in saveTicketConfigs:', error)
  }
}

// 保存-卖品 配置
function saveGoodsConfigs() {
  console.log('保存-卖品 配置', goodSetting.value)

  // 验证 mainInfo.value.couponRules 是否存在且长度大于0
  if (
    !Array.isArray(mainInfo.value.couponRules)
    || mainInfo.value.couponRules.length === 0
  ) {
    console.error('mainInfo.value.couponRules 无效或为空')
    return []
  }

  // 验证 goodSetting.value 是否存在
  if (!goodSetting.value) {
    console.error('goodSetting.value 无效')
    return []
  }

  try {
    const ruleId = mainInfo.value.couponRules[0].ruleId

    const config = [
      {
        couponGoodsSelectScopeConfig: {
          ruleId,
          cinemaScope: goodSetting.value.cinemaScope,
          goodsSelectScope: goodSetting.value.goodsSelectScope,
          goodsTypeScope: goodSetting.value.goodsTypeScope,
          goodsInfoScope: goodSetting.value.goodsInfoScope,
        },
        couponCinemas: goodSetting.value.cinemaIds.map(item => ({
          ruleId,
          cinemaId: item.cinemaId,
          cinemaName: item.name,
        })),
        couponGoodsTypes: goodSetting.value.goodsTypeIds.map(item => ({
          ruleId,
          goodsTypeId: item,
          goodsTypeName: item.name, // 假设 item.name 存在，否则需要处理
        })),
        couponGoodsInfos:
          goodSetting.value.cinemaGoodsInfoList?.map(item => ({
            ruleId,
            cinemaId: item.cinemaId,
            goodsId: item.goodsInfoId ? item.goodsInfoId : item.goodsId,
            goodsName: item.goodsName,
            cinemaName: item.cinemaName,
          })) || [],
      },
    ]

    console.log('config:', config)
    return config
  }
  catch (error) {
    console.error('Error in saveGoodsConfigs:', error)
    return []
  }
}

// 保存-演出 配置
function saveShowConfigs() {
  // 其他逻辑
  console.log('保存-演出 配置', showSetting.value)

  const couponShows = []

  showSetting.value.showScheduleList.map(item => ({
    ruleId: mainInfo.value.couponRules[0].ruleId,
    showScheduleId: item.selectScheduleIds.map((schedule) => {
      couponShows.push({
        couponId: mainInfo.value.id,
        ruleId: mainInfo.value.couponRules[0].ruleId,
        showScheduleId: schedule,
        cinemaId: item.cinemaId,
      })
    }),
    // cinemaId: item,
  }))

  const config = [
    {
      couponShowSelectScopeConfig: {
        showScope: showSetting.value.showScope,
      },
      couponCinemas: showSetting.value.showScheduleList.map(item => ({
        couponId: mainInfo.value.id,
        ruleId: mainInfo.value.couponRules[0].ruleId,
        cinemaId: item.cinemaId,
        cinemaName: item.cinemaName,
        showSchedules: item.showSchedules,
      })),
      couponShows,
    },
  ]
  console.log('config:', config)
  return config
}
// 保存-展览 配置
function saveExhibitionConfigs() {
  console.log('保存-展览 配置', exhibitionConfigs.value)
  const hallConfig = []
  exhibitionConfigs.value.exhibitionCinemaHallIds.map((item) => {
    if (item.halls && item.halls.length > 0) {
      item.halls.map((hall) => {
        hallConfig.push({
          ruleId: mainInfo.value.couponRules[0].ruleId,
          couponId: mainInfo.value.id,
          cinemaId: item.cinemaId,
          hallId: hall,
        })
      })
    }
    else {
      hallConfig.push({
        ruleId: mainInfo.value.couponRules[0].ruleId,
        couponId: mainInfo.value.id,
        cinemaId: item.cinemaId,
      })
    }
  })
  const filmConfig = exhibitionConfigs.value.exhibitionFilmIds.map(item => ({
    ruleId: mainInfo.value.couponRules[0].ruleId,
    filmId: item,
  }))

  return [
    {
      couponExhibitionSelectScopeConfig: {
        ruleId: mainInfo.value.couponRules[0].ruleId,
        cinemaScope: exhibitionConfigs.value.exhibitionCinemaScope,
        filmScope: exhibitionConfigs.value.exhibitionFilmScope,
        periodScope: 0,
      },
      couponCinemas: hallConfig,
      couponFilms: filmConfig,
    },
  ]
}

// 保存-配置
function saveConfigs() {
  console.log('保存配置')

  // 验证 mainInfo.value 是否存在
  if (!mainInfo.value || !mainInfo.value.id) {
    console.error('mainInfo.value 无效或缺少 id')
    proxy.$modal.msgError('优惠券信息无效，请检查数据！')
    return
  }

  // 验证 useOn 的合法性
  if (![0, 1, 2, 3].includes(useOn.value)) {
    console.error('useOn 的值无效:', useOn.value)
    proxy.$modal.msgError('券类型无效，请检查数据！')
    return
  }

  try {
    const postData = {
      couponMainInfo: {
        id: mainInfo.value.id,
      },
    }

    switch (useOn.value) {
      case 0: // 电影券
        postData.couponTicketConfigs = saveTicketConfigs()
        break
      case 1: // 卖品券
        postData.couponGoodsConfigs = saveGoodsConfigs()
        break
      case 2: // 演出券
        postData.couponShowConfigs = saveShowConfigs()
        break
      case 3: // 展览券
        postData.couponExhibitionConfigs = saveExhibitionConfigs()
        break
      default:
        console.error('未处理的 useOn 值:', useOn.value)
        proxy.$modal.msgError('券类型无效，请检查数据！')
        return
    }

    console.log('postData:', postData)

    proxy.$modal
      .confirm('是否确认保存？')
      .then(() => {
        proxy.$modal.loading('正在保存...')
        stepCouponConfigUpdate(postData)
          .then((res) => {
            console.log(res)
            if (res.code === 200) {
              proxy.$modal.msgSuccess('保存成功')
              close()
            }
            else {
              proxy.$modal.msgError(res.msg)
            }
            proxy.$modal.closeLoading()
          })
          .catch((err) => {
            console.error('保存失败:', err)
            proxy.$modal.msgError('保存失败，请重试')
            proxy.$modal.closeLoading()
          })
      })
      .catch(() => {
        console.log('取消保存')
        proxy.$modal.msgError('取消保存')
      })
  }
  catch (error) {
    console.error('Error in saveConfigs:', error)
    proxy.$modal.msgError('保存过程中发生错误，请重试')
  }
}

// 关闭
function close() {
  proxy.$emit('close')
}
</script>

<template>
  <div>
    <!-- <pre v-is-dev>{{ ticketConfigs }}</pre> -->
    <!-- 电影券--多对一券 -->
    <div v-if="useOn === 0">
      <div v-if="mainInfo.couponType === 4">
        <el-card
          v-for="(item, index) in mainInfo.couponRules"
          shadow="never"
          style="margin-bottom: 10px"
        >
          <template #header>
            规则{{ index + 1 }} : {{ item.priceReduce }}兑换{{
              item.priceOrigin
            }}
            <el-tag>{{ item.id }}</el-tag>
          </template>
          <addCounponSettingTicket v-model="ticketConfigs[index]" />
        </el-card>
      </div>
      <div v-else>
        <addCounponSettingTicket v-model="ticketConfigs[0]" />
      </div>
    </div>
    <!-- <pre>
      {{ goodSetting }}
    </pre> -->
    <!--        卖品券限制条件Star -->
    <add-counpon-setting-goods v-if="useOn === 1" v-model="goodSetting" />
    <!--        卖品券限制条件End -->

    <!-- 演出限制条件Star -->
    <add-counpon-setting-show v-if="useOn === 2" v-model="showSetting" />
    <add-counpon-setting-exhibition
      v-if="useOn === 3"
      v-model="exhibitionConfigs"
    />
    <!-- 演出限制条件End -->
    <el-button type="primary" @click="saveConfigs">
      保存
    </el-button>
    <el-button @click="close">
      取 消
    </el-button>
  </div>
</template>

<style scoped lang="scss">
/* 组件样式 */
.coupon-setting {
  /* 预留样式空间 */
}
</style>
