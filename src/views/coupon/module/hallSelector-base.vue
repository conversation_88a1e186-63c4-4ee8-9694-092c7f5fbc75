<script setup>
import {listHall} from "@/api/cinemas/hall";
import {getCurrentInstance, onMounted, ref, watch} from "vue";

const {proxy} = getCurrentInstance()
const emit = defineEmits(['update:modelValue'])

const {cinema_hall_type} = proxy.useDict('cinema_hall_type')

// 引入字典
// 组件中声明emits
const props = defineProps({
  cinemaId: {
    type: Number,
    request: true
  },
  modelValue: {
    type: Array,
    default: () => {
      return [];
    }
  }
})
const halls = ref([])
// 筛选halls的hallType
const hallTypes = ref([]);

const loading = ref(true)

// 根据影院id列表获取影厅列表
const getHallByIds = (cinemaId) => {
  //  loading.value = false
  // proxy.$modal.loading('加载中...')
  loading.value = true
  if (!cinemaId) {
    proxy.$modal.msgError('请选择影院')
     loading.value = false
    return
  }
  listHall({cinemaId}).then((res) => {
    console.log(res)
    const {code, data} = res
    halls.value = data
    hallTypes.value = [...new Set(halls.value.map((hall) => hall.hallType))];
    loading.value = false
  })
  //  loading.value = false

}
onMounted(() => {
  getHallByIds(props.cinemaId)
})

const selectedHallList = ref(props.modelValue)
// console.log(hallTypes.value)
// 根据hallType筛选halls，根据筛选的hallType更新selectedHallList
const ckFilterHalls = (hallType) => {
  const newHalls = halls.value.filter((hall) => hall.hallType === hallType);
  newHalls.forEach((hall) => {
    if (!selectedHallList.value.includes(hall.id)) {
      selectedHallList.value.push(hall.id);
    } else {
      selectedHallList.value = selectedHallList.value.filter((hallId) => hallId !== hall.id);
    }
  });
};

// 快选影厅类型格式化
const formatHallType = (hallType) => {
  // console.log('字典',cinema_hall_type._object.cinema_hall_type);

  const hallTypeObj = cinema_hall_type._object.cinema_hall_type.find(item => item.value === hallType);
  return hallTypeObj?.label || hallType;
}


//
// // 监听selectedHallList变化
watch(selectedHallList, (newVal, oldVal) => {
  emit('update:modelValue', newVal);
});

watch(props.modelValue, (newVal, oldVal) => {
  emit('update:modelValue', newVal);
});

</script>

<template>
  <div>
    <div>快速选择：
      <el-tag v-for="hallType in hallTypes" :key="hallType" type="primary" size="small" effect="dark"
              style="margin-right: 5px; cursor: pointer;"
              @click="ckFilterHalls(hallType)">
        {{ formatHallType(hallType) }}
      </el-tag>
      <el-button type="text" size="small" @click="selectedHallList=[]">清空</el-button>
    </div>
    <!-- {{ props.cinemaId }}
   {{ props.modelValue }} -->
    <el-select style="width: 90%" :disabled="loading" :suffix-icon="loading?'loading':'ArrowDown'" clearable effect="light" size="large" v-model="selectedHallList"
               multiple placeholder="不限制影厅">
      <el-option v-for="hall in halls" :key="hall.id" :label="hall.hallName" :value="hall.id">
        <span style="float: left">{{ hall.hallName }}</span>
        <span style="
          float: right;
          color: var(--el-text-color-secondary);
          font-size: 13px;
        ">{{ formatHallType(hall.hallType) }}</span>
      </el-option>
    </el-select>
  </div>
</template>

<style scoped lang="scss">
.hall-selection {
  //margin-bottom: 10px;
  width: 100%;
  margin-left: -10px;
  border: 1px solid #ebeef5;
}
</style>
