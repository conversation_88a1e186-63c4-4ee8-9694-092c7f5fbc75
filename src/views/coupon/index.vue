<!--
@Author: z<PERSON><PERSON>
@Date: 2024-1-5
 * @LastEditors: kevinz<PERSON> <EMAIL>
 * @LastEditTime: 2025-04-25 16:48:50
 * @FilePath: \psdv6-manage-pro\src\views\activity\coupon\index.vue
@Description: 优惠活动-优惠券 列表
-->

<script setup name="couponList">
import { getCurrentInstance, reactive, ref, toRefs } from 'vue'
// import { listChannel } from '@/api/channel'
import {
  stepCouponInfoChange,
  stepCouponInfoCopy,
  stepCouponInfoDel,
  stepCouponInfoList,
  stepCouponMainDetail,
} from '@/api/modules/coupons/index.js'
import addCouponCom from './module/addCounponCom.vue'
import couponCode from './module/couponCode.vue'
import couponCodeQuery from './module/couponCodeQuery.vue'
import couponDetailCom from './module/couponDetail.vue'

const { proxy } = getCurrentInstance()
const aa = proxy.useDict('bus_film_type')
console.log(aa)

const couponList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const daterangeCreateTime = ref([])

let editCoupon = reactive(null) // 编辑券数据
// 渠道列表
const channelList = ref([])
// 显示隐藏券信息
const showCouponDialog = ref(false)
const showCouponDetail = ref(false)
const showCouponCode = ref(false)
const showCouponCodeQuery = ref(false)
let couponData = reactive(null)

const data = reactive({
  form: {},
  queryParams: {
    id: '',
    pageNum: 1, // 当前页码
    pageSize: 10, // 每页条数
    name: undefined, // 优惠券名称
    useOn: '', // 适用类型 0,电影票 1,卖品 2.演出
    applyFilmType: '', // 适用影片版本
    applyCinema: undefined, // 适用影院
    couponType: '', // 优惠券类型
    validStartTime: undefined, // 有效开始时间
    validEndTime: undefined, // 有效结束时间
    useStatus: '', // 使用状态 0关闭，1开启
    generateType: '', // 生成数量 0：需要时生成 1：一次性生成
    isAsc: 'desc',
    orderByColumn: 'id',
  },
  rules: {
    testKey: [{ required: true, message: 'key键不能为空', trigger: 'blur' }],
    value: [{ required: true, message: '值不能为空', trigger: 'blur' }],
  },
})

const { queryParams } = toRefs(data)

const _codeMap = {
  0: '不限影院',
  1: '指定影院',
  2: '排除影院',
}

/** 查询OSS对象存储列表 */
function getList() {
  loading.value = true
  console.log(
    '自定义分页查询',
    proxy.addDateRange(
      queryParams.value,
      daterangeCreateTime.value,
      'CreateTime',
    ),
  )
  stepCouponInfoList(
    proxy.addDateRange(
      queryParams.value,
      daterangeCreateTime.value,
      'CreateTime',
    ),
  ).then((response) => {
    // console.log("stepCouponInfoList", response);
    couponList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeCreateTime.value = []
  proxy.resetForm('queryRef')
  handleQuery()
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  editCoupon = null
  showCouponDialog.value = true
}

async function handleDetail(row, mode) {
  // loading
  proxy.$modal.loading('正在加载')
  console.log({ row, mode })
  let res = null
  res = await stepCouponMainDetail(row.id)

  console.log('详情', res)
  proxy.$modal.closeLoading()
  if (mode == 'view') {
    editCoupon = {
      mode: mode || 'view',
      data: res.data,
    }
  }
  couponData = res.data
  showCouponDetail.value = true
}

async function handleEditBase(coupon, model) {
  console.log('修改主信息', { coupon }, { model })
  const res = null
  // res = await stepCouponMainDetail(coupon.id);
  // const { code, data, msg } = res;
  // console.log({ code, data, msg });

  editCoupon = {
    mode: model || 'view',
    step: 1,
    data: coupon,
  }
  showCouponDialog.value = true
}

async function handleEditConfig(coupon, model) {
  console.log('修改从信息', { coupon }, { model })
  editCoupon = {
    mode: model || 'view',
    step: 2,
    data: coupon,
  }
  showCouponDialog.value = true
}

function handleEdit(data) {
  handleDetail(data, 'edit')
}

// 启用禁用
function onCouponUseStatusChange(row) {
  // couponStatusToggle({ id: row.id, useStatus: row.useStatus.code }).then(
  //   (res) => {
  //     proxy.$modal.msgSuccess("操作成功");
  //     getList();
  //   }
  // );
  console.log('启用禁用', row)
  stepCouponInfoChange({ id: row.id, useStatus: row.couponCodeCreateConfig.useStatus }).then(
    (res) => {
      proxy.$modal.msgSuccess('操作成功')
      getList()
    },
  ).catch(() => {
    // row.couponCodeCreateConfig.useStatus = !row.couponCodeCreateConfig.useStatus
    getList()
  })
}

/** 修改按钮操作 */
function handleCopy(row) {
  proxy.$modal
    .confirm('确认复制优惠券？')
    .then(() => {
      loading.value = true
      // console.log(row);
      // if (row.useOn.name == "GOODS") {
      //   return goodsCouponCopy({ id: row.id });
      // } else if (row.useOn.name == "FILM") {
      //   return couponCopy({ id: row.id });
      // } else if (row.useOn.name == "SHOW") {
      //   return showCouponCopy({ id: row.id });
      // }
      stepCouponInfoCopy(row.id).then((res) => {
        loading.value = false
        getList()
        proxy.$modal.msgSuccess('已复制')
      })
    })
  // .then(() => {
  //   loading.value = false;
  //   getList();
  //   proxy.$modal.msgSuccess("已复制");
  // })
  // .finally(() => {
  //   loading.value = false;
  // });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row.id || ids.value
  proxy.$modal
    .confirm('确认删除该优惠券吗?')
    .then(() => {
      loading.value = true
      stepCouponInfoDel(ids).then((res) => {
        loading.value = false
        getList()
        proxy.$modal.msgSuccess('删除成功')
      }).catch(() => {
        loading.value = false
      })
      // // return couponDel(ids);
      // if (row.useOn.name == "GOODS") {
      //   return goodsCouponDel(ids);
      // } else if (row.useOn.name == "FILM") {
      //   return couponDel(ids);
      // } else if (row.useOn.name == "SHOW") {
      //   return showCouponDel(ids);
      // }
    })
  // .then(() => {
  //   loading.value = false;
  //   getList();
  //   proxy.$modal.msgSuccess("删除成功");
  // })
  // .finally(() => {
  //   loading.value = false;
  // });
}

// 获取渠道
function getListChannel() {
  // listChannel({ pageNum: 1, pageSize: 30 }).then((res) => {
  //   let { rows = [] } = res
  //   channelList.value = rows.map((item) => {
  //     return { id: item.id, label: item.channelName }
  //   })
  // })
  channelList.value = [
    { id: 0, label: '全部' },
    { id: 1, label: '抖音' },
    { id: 2, label: '快手' },
    { id: 3, label: '微信' },
    { id: 4, label: '抖音' },
  ]
}

getListChannel()

getList()

function handleShowCouponCode(data) {
  couponData = data
  showCouponCode.value = true
}

function handleCouponCodeQuery() {
  showCouponCodeQuery.value = true
}
</script>

<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryRef" :model="queryParams" size="small" :inline="true">
      <el-form-item label="优惠券ID" aria-label="优惠券ID" prop="id">
        <el-input
          v-model="queryParams.id" placeholder="请输入优惠券ID" clearable aria-label="优惠券ID" style="width: 200px;"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="优惠券名称" aria-label="优惠券名称" prop="name">
        <el-input
          v-model="queryParams.name" placeholder="请输入优惠券名称" clearable aria-label="优惠券名称" style="width: 200px;"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="适用类型" prop="useOn">
        <el-select v-model="queryParams.useOn" placeholder="请选择适用类型" style="width: 200px;">
          <el-option label="电影票" aria-label="电影票" :value="0" />
          <el-option label="卖品" aria-label="卖品" :value="1" />
          <el-option label="演出" aria-label="演出" :value="2" />
          <el-option label="展览" aria-label="展览" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="渠道" prop="channelId">
        <el-select v-model="queryParams.channelId" placeholder="请选择渠道" style="width: 200px;">
          <el-option v-for="item in channelList" :key="item.id" :label="item.label" aria-label="电影票" :value="item.id" />
        </el-select>
      </el-form-item>
      <!--
      <el-form-item label="影片版本" aria-label="影片版本" prop="applyFilmType">
        <el-select v-model="queryParams.applyFilmType" clearable placeholder="请选择影片版本" style="width: 200px">
          <el-option label="全部" value="" />
          <el-option v-for="dict in bus_film_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="优惠券类型" prop="couponType">
        <el-select v-model="queryParams.couponType" placeholder="请选择优惠券类型" style="width: 200px;">
          <el-option label="全部" value="" />
          <el-option label="满减券" :value="0" />
          <el-option label="减至券" :value="1" />
          <el-option label="通兑券" :value="2" />
          <el-option label="折扣券" :value="3" />
          <el-option label="多对一券" :value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="生成方式" prop="generateType">
        <el-select v-model="queryParams.generateType" placeholder="请选择生成方式" style="width: 200px;" clearable>
          <el-option label="全部" value="" />
          <el-option label="一次性生成" value="1" />
          <el-option label="需要时生成" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="使用状态" prop="useStatus">
        <el-select v-model="queryParams.useStatus" placeholder="请选择使用状态" style="width: 200px;">
          <el-option label="全部" value="" />
          <el-option label="开启" :value="1" />
          <el-option label="关闭" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="有效日期">
        <el-date-picker
          v-model="daterangeCreateTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="[
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 1, 1, 23, 59, 59),
          ]" style="width: 260px;"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="search" @click="handleQuery">
          查询
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">
          重置
        </el-button>
        <el-button type="success" icon="Search" @click="handleCouponCodeQuery">
          查询券码
        </el-button>
        <el-button type="primary" icon="Plus" @click="handleAdd">
          新增券
        </el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading" :data="couponList" size="small" @selection-change="handleSelectionChange"
      @row-dblclick="handleDetail"
    >
      <!-- <el-table-column type="selection" width="55" align="center"/> -->
      <el-table-column label="优惠券ID" aria-label="优惠券ID" align="center" prop="id" width="100">
        <template #default="scope">
          <el-text size="small">
            {{ scope.row.id }}
          </el-text>
        </template>
      </el-table-column>
      <el-table-column label="优惠券名称" align="center" prop="name" width="160" />
      <el-table-column label="适用商品" align="center" prop="useOn">
        <template #default="scope">
          <el-tag v-if="scope.row.useOn === 0" type="success" round>
            电影票
          </el-tag>
          <el-tag v-if="scope.row.useOn === 1" type="primary" round>
            卖品
          </el-tag>
          <el-tag v-if="scope.row.useOn === 2" type="warning" round>
            演出
          </el-tag>
          <el-tag v-if="scope.row.useOn === 3" type="info" round>
            展览
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="券类型" align="center" prop="couponType">
        <template #default="scope">
          <el-tag v-if="scope.row.couponType === 0" type="success">
            满减券
          </el-tag>
          <el-tag v-else-if="scope.row.couponType === 1" type="warning">
            减至券
          </el-tag>
          <el-tag v-else-if="scope.row.couponType === 2">
            通兑券
          </el-tag>
          <el-tag v-else-if="scope.row.couponType === 3" type="danger">
            折扣券
          </el-tag>
          <el-tag v-else-if="scope.row.couponType === 4" type="info">
            多兑一券
          </el-tag>
          <span v-else>{{ scope.row.couponType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="适用渠道" align="center" prop="cinemaScope" width="140">
        <template #default="{ row }">
          <el-popover placement="bottom" trigger="hover" width="230">
            <el-card shadow="never">
              <template #header>
                <span>适用渠道：</span>
              </template>
              <el-tag v-for="(item, index) in row?.couponChannels" :key="index" style="margin: 2px;" title="包含渠道">
                {{ item.channelName }}
              </el-tag>
            </el-card>

            <template #reference>
              <!-- {{ row.couponChannels }} -->
              <span>包含渠道<el-text type="primary">{{ row?.couponChannels.length }}</el-text>个</span>
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="适用影院" align="center" prop="cinemaScope" width="140">
        <template #default="{ row }">
          <div v-if="row.couponRuleCinemas.length">
            <!-- {{ row.couponRuleCinemas }} -->
            <el-popover placement="bottom" :title="row.cinemaScopeText" :width="400" trigger="hover">
              <el-card
                v-for="(item, index) in row.couponTicketConfigs" :key="index" shadow="never"
                style="margin-bottom: 10px;"
              >
                <template #header>
                  <!--                  {{item.couponTicketSelectScopeConfig}} -->
                  <el-text v-if="item.couponTicketSelectScopeConfig.cinemaScope === 0" type="primary">
                    规则{{ index + 1 }} 全部影院
                  </el-text>
                  <el-text v-else-if="item.couponTicketSelectScopeConfig.cinemaScope === 1" type="success">
                    规则{{ index + 1 }} 指定影院
                  </el-text>
                  <el-text v-else type="danger">
                    规则{{ index + 1 }} 排除影院
                  </el-text>
                </template>

                <div v-if="item.couponTicketSelectScopeConfig.cinemaScope === 1">
                  <el-tag
                    v-for="(cinema, index) in row.couponRuleCinemas.find(item => item.ruleId === row.couponTicketConfigs[index].couponTicketSelectScopeConfig.ruleId).cinemaNames || []"
                    :key="index"
                    type="success" size="small" style="margin: 2px;"
                  >
                    <span>{{ cinema }}</span>
                  </el-tag>
                </div>
                <div v-else-if="item.couponTicketSelectScopeConfig.cinemaScope === 2">
                  <el-tag
                    v-for="(cinema, index) in row.couponRuleCinemas.find(item => item.ruleId === row.couponTicketConfigs[index].couponTicketSelectScopeConfig.ruleId).cinemaNames || []"
                    :key="index"
                    type="danger" size="small" style="margin: 2px;"
                  >
                    <span>{{ cinema }}</span>
                  </el-tag>
                </div>

                <!--                指定影院{{ item.cinemaNames }}家 -->
              </el-card>
              <template #reference>
                <el-button link type="primary" size="small">
                  {{ row.couponTicketConfigs.length }} 组规则
                </el-button>
              </template>
            </el-popover>
          </div>
          <div v-else>
            不限影院
          </div>
        </template>
      </el-table-column>
      <el-table-column label="生成方式" align="center" prop="num" width="140">
        <template #default="{ row }">
          <span v-if="row.couponCodeCreateConfig.generateType">一次性生成
            <el-tag type="success">{{ row.couponCodeCreateConfig.num }} </el-tag>
            张</span>
          <span v-else>
            <el-tag type="warning">需要时生成</el-tag>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="券绑定用户数" align="center" prop="bindCount" width="110" />
      <el-table-column label="券已使用数" align="center" prop="useCount" width="90" />
      <el-table-column label="有效日期" align="center" width="180">
        <template #default="scope">
          <span v-if="scope.row.couponCanuseConfig.validScope">
            绑定后 <el-tag type="success">{{ scope.row.couponCanuseConfig.overdueDay }}</el-tag> 天内有效
          </span>
          <span v-else>{{ scope.row.couponCanuseConfig.startTime }} 至 {{ scope.row.couponCanuseConfig.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="使用状态" align="center" width="180">
        <template #default="{ row }">
          <!-- {{ row.couponCodeCreateConfig.useStatus }} -->
          <el-switch
            v-model="row.couponCodeCreateConfig.useStatus" size="large" inline-prompt active-text="开启"
            inactive-text="关闭" :active-value="1" :inactive-value="0" @change="onCouponUseStatusChange(row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160" fixed="right" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <div class="flex">
            <el-button type="primary" link size="small" @click="handleDetail(row, 'view')">
              详情
            </el-button>
            <el-button type="primary" class="marr8" link size="small" @click="handleShowCouponCode(row)">
              查看券码
            </el-button>
            <!-- <el-popconfirm
                title="确认复制优惠券？"
                @confirm="handleCopy(row)">
                <template #reference>
                    <el-button type="primary" link>复制</el-button>
                </template>
  </el-popconfirm> -->
            <el-dropdown trigger="click">
              <el-button type="primary" link size="small">
                更多
                <el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item icon="CopyDocument" @click="handleCopy(row)">
                    复制
                  </el-dropdown-item>
                  <!-- <el-dropdown-item v-if="row.useStatus.code == '0'" @click="handleDetail(row, 'edit')"
                    icon="Edit">修改</el-dropdown-item> -->
                  <!-- 编辑主信息 -->
                  <el-dropdown-item style="color: #2ecc71;" icon="Edit" @click="handleEditBase(row, 'edit')">
                    修改主信息
                  </el-dropdown-item>
                  <!-- 编辑从配置 -->
                  <el-dropdown-item style="color: #9b59b6;" icon="Edit" @click="handleEditConfig(row, 'edit')">
                    修改条件
                  </el-dropdown-item>
                  <el-dropdown-item style="color: red;" icon="delete" @click="handleDelete(row)">
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      :total="total" @pagination="getList"
    />

    <!-- 新增、修改券 -->
    <add-coupon-com
      v-if="showCouponDialog" :model-value="showCouponDialog" :edit-coupon="editCoupon"
      @update:model-value="showCouponDialog = $event" @refresh="getList" @close="showCouponDialog = false"
    />

    <!-- 查看券 -->
    <!-- <coupon-detail-com v-if="showCouponDetail" :model-value="showCouponDetail" :couponData="couponData"
      @update:model-value="showCouponDetail = $event" @refresh="getList" @edit="handleEdit"
      @close="showCouponDetail = false">
    </coupon-detail-com> -->
    <!-- <pre v-is-dev>
    {{ couponData }}
   </pre> -->
    <coupon-detail-com
      v-if="showCouponDetail" :model-value="showCouponDetail" :coupon-data="couponData"
      @close="showCouponDetail = false"
    />

    <!-- 券码 -->
    <coupon-code
      v-if="showCouponCode" :coupon-data="couponData" :model-value="showCouponCode"
      @update:model-value="showCouponCode = $event" @refresh="getList" @close="showCouponCode = false"
    />

    <!-- 券码查询结果 -->
    <coupon-code-query
      v-if="showCouponCodeQuery" :model-value="showCouponCodeQuery"
      :channel-list="channelList" @close="showCouponCodeQuery = false"
    />
  </div>
</template>

<style scoped lang="scss">
.flex {
  display: flex;
}

.marr8 {
  margin-right: 8px;
}
</style>
