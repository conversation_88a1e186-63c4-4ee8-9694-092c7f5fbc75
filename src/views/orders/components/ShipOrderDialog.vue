<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import type { Order, ShipFormData, ShipOrderParams } from '../types'
import {
  ElButton,
  ElDescriptions,
  ElDescriptionsItem,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElText,
} from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'

defineOptions({
  name: 'ShipOrderDialog',
})

const props = defineProps<Props>()

const emit = defineEmits<Emits>()

interface Props {
  modelValue: boolean
  order: Order | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', params: ShipOrderParams): void
}

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive<ShipFormData>({
  companyName: '',
  trackingNumber: '',
})

// 提交状态
const submitting = ref(false)

// 表单验证规则
const rules: FormRules<ShipFormData> = {
  companyName: [
    { required: true, message: '请输入物流公司名称', trigger: 'blur' },
    { min: 2, max: 50, message: '物流公司名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  trackingNumber: [
    { required: true, message: '请输入运单号', trigger: 'blur' },
    { min: 5, max: 30, message: '运单号长度在 5 到 30 个字符', trigger: 'blur' },
    { pattern: /^[A-Z0-9]+$/i, message: '运单号只能包含字母和数字', trigger: 'blur' },
  ],
}

// 常用物流公司
const commonCompanies = [
  '顺丰速运',
  '中通快递',
  '圆通速递',
  '申通快递',
  '韵达速递',
  '百世快递',
  '德邦快递',
  '京东物流',
  '邮政EMS',
]

// 格式化金额
function formatAmount(amount: number) {
  return (amount / 100).toFixed(2)
}

// 重置表单
function resetForm() {
  formRef.value?.resetFields()
  Object.assign(formData, {
    companyName: '',
    trackingNumber: '',
  })
}

// 快速选择物流公司
function selectCompany(company: string) {
  formData.companyName = company
}

// 确认发货
async function handleConfirm() {
  if (!props.order) { return }

  try {
    await formRef.value?.validate()

    submitting.value = true

    const params: ShipOrderParams = {
      id: props.order.id,
      companyName: formData.companyName.trim(),
      trackingNumber: formData.trackingNumber.trim(),
    }

    emit('confirm', params)
  }
  catch (error) {
    console.error('表单验证失败:', error)
  }
  finally {
    submitting.value = false
  }
}

// 取消操作
function handleCancel() {
  resetForm()
  dialogVisible.value = false
}

// 监听对话框关闭，重置表单
watch(dialogVisible, (visible) => {
  if (!visible) {
    resetForm()
  }
})
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="订单发货"
    width="600px"
    :close-on-click-modal="false"
    @close="handleCancel"
  >
    <div v-if="order" class="ship-order-dialog">
      <!-- 订单信息 -->
      <div class="mb-6">
        <h3 class="mb-3 text-lg font-medium">
          订单信息
        </h3>
        <ElDescriptions :column="2" border>
          <ElDescriptionsItem label="订单编号">
            <ElText class="font-mono">
              {{ order.id }}
            </ElText>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="实付金额">
            <ElText class="text-red-600 font-medium">
              ¥{{ formatAmount(order.payAmount) }}
            </ElText>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="商品数量">
            {{ order.itemList?.length || 0 }}件
          </ElDescriptionsItem>
          <ElDescriptionsItem label="收货信息ID">
            {{ order.shippingInfoId || '-' }}
          </ElDescriptionsItem>
        </ElDescriptions>
      </div>

      <!-- 发货表单 -->
      <div>
        <h3 class="mb-3 text-lg font-medium">
          物流信息
        </h3>
        <ElForm
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="100px"
        >
          <ElFormItem label="物流公司" prop="companyName">
            <ElInput
              v-model="formData.companyName"
              placeholder="请输入物流公司名称"
              clearable
            />
            <div class="mt-2">
              <ElText size="small" class="mr-2 text-gray-500">
                常用:
              </ElText>
              <ElButton
                v-for="company in commonCompanies"
                :key="company"
                type="text"
                size="small"
                class="mb-1 mr-2"
                @click="selectCompany(company)"
              >
                {{ company }}
              </ElButton>
            </div>
          </ElFormItem>

          <ElFormItem label="运单号" prop="trackingNumber">
            <ElInput
              v-model="formData.trackingNumber"
              placeholder="请输入运单号"
              clearable
            />
            <div class="mt-1">
              <ElText size="small" class="text-gray-500">
                运单号只能包含字母和数字，长度5-30位
              </ElText>
            </div>
          </ElFormItem>
        </ElForm>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end space-x-3">
        <ElButton @click="handleCancel">
          取消
        </ElButton>
        <ElButton
          type="primary"
          :loading="submitting"
          @click="handleConfirm"
        >
          确认发货
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.ship-order-dialog {
  max-height: 60vh;
  overflow-y: auto;
}

.font-mono {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}

:deep(.el-form-item__content) {
  flex-direction: column;
  align-items: flex-start;
}

:deep(.el-button--text) {
  padding: 2px 4px;
  height: auto;
  font-size: 12px;
}
</style>
