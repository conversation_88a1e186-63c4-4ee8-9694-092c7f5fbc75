<route lang="yaml">
meta:
  title: 部门管理
  icon: i-ri:building-line
  # auth: ''
</route>

<script setup lang="ts">
import { ElMessageBox } from 'element-plus'
import { nextTick, onMounted, ref } from 'vue'
import { toast } from 'vue-sonner'
import apiSystem from '@/api/modules/system/system.ts'
import DepartmentSelector from '@/components/DepartmentSelector/index.vue'

defineOptions({
  name: 'SystemDepartment',
})

// 部门数据接口
interface Department {
  id: string
  name: string
  parentId?: string
  sort?: number
  ctime: number
  children?: Department[]
}

// 响应式数据
const loading = ref(false)
const departmentList = ref<Department[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索条件
const searchForm = ref({
  name: '',
  parentId: '',
})

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('新增部门')
const dialogLoading = ref(false)
const isEdit = ref(false)

// 表单数据
const formData = ref({
  id: '',
  name: '',
  parentId: '',
  sort: 0,
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 50, message: '部门名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  sort: [
    { type: 'number' as const, min: 0, message: '排序值不能小于0', trigger: 'blur' },
  ],
}

// 表单引用
const formRef = ref()
const nameInputRef = ref()

// 部门树数据（用于选择父部门）
const departmentTreeData = ref<Department[]>([])
const departmentTreeLoading = ref(false)

// 监听对话框关闭，清理状态
function handleDialogClose() {
  // 重置表单数据
  formData.value = {
    id: '',
    name: '',
    parentId: '',
    sort: 0,
  }

  // 重置编辑状态
  isEdit.value = false
  dialogTitle.value = '新增部门'
}

// 监听对话框打开，刷新部门树数据
function handleDialogOpen() {
  // 刷新部门树数据
  getDepartmentTree()
}

// 获取部门列表
async function getDepartmentList() {
  loading.value = true
  try {
    const res: any = await apiSystem.getDepartmentList({
      page: currentPage.value - 1,
      size: pageSize.value,
      parentId: searchForm.value.parentId || undefined,
      name: searchForm.value.name || undefined,
    })
    if (res.code === 0) {
      // 将扁平数据转换为树形结构进行层级展示
      const flatData = res.data.content || []
      departmentList.value = buildDepartmentTree(flatData)
      total.value = res.data.total || 0
    }
    else {
      toast.error(res.msg || '获取部门列表失败')
    }
  }
  catch (error: any) {
    toast.error(error.message || '获取部门列表失败')
  }
  finally {
    loading.value = false
  }
}

// 获取部门树数据（用于选择父部门）
async function getDepartmentTree() {
  departmentTreeLoading.value = true
  try {
    const res: any = await apiSystem.getDepartmentList({
      page: 0,
      size: 1000, // 获取所有部门用于构建树
    })
    if (res.code === 0) {
      // 将扁平数据转换为树形结构
      departmentTreeData.value = buildDepartmentTree(res.data.content || [])
    }
    else {
      toast.error(res.msg || '获取部门数据失败')
    }
  }
  catch (error: any) {
    toast.error(error.message || '获取部门数据失败')
  }
  finally {
    departmentTreeLoading.value = false
  }
}

// 构建部门树结构
function buildDepartmentTree(departmentList: Department[]): Department[] {
  const departmentMap = new Map<string, Department>()
  const rootDepartments: Department[] = []

  // 创建部门映射
  departmentList.forEach((department) => {
    departmentMap.set(department.id, { ...department, children: [] })
  })

  // 构建树形结构
  departmentList.forEach((department) => {
    const departmentNode = departmentMap.get(department.id)!
    if (department.parentId && departmentMap.has(department.parentId)) {
      const parentNode = departmentMap.get(department.parentId)!
      parentNode.children!.push(departmentNode)
    }
    else {
      rootDepartments.push(departmentNode)
    }
  })

  return rootDepartments
}

// 打开新增对话框
async function openAddDialog() {
  isEdit.value = false
  dialogTitle.value = '新增部门'

  // 重置表单数据
  formData.value = {
    id: '',
    name: '',
    parentId: '',
    sort: 0,
  }

  // 确保部门树数据已加载
  await getDepartmentTree()

  // 打开对话框
  dialogVisible.value = true

  // 等待DOM更新后聚焦输入框
  nextTick(() => {
    // 聚焦到部门名称输入框
    if (nameInputRef.value) {
      nameInputRef.value.focus()
    }
  })
}

// 打开编辑对话框
async function openEditDialog(row: Department) {
  isEdit.value = true
  dialogTitle.value = '编辑部门'
  console.log(row)

  // 确保部门树数据已加载
  // await getDepartmentTree()

  // 设置表单数据
  formData.value = {
    id: row.id,
    name: row.name,
    parentId: row.parentId || '',
    sort: row.sort || 0,
  }

  // 打开对话框
  dialogVisible.value = true

  // 等待DOM更新后聚焦输入框
  nextTick(() => {
    // 聚焦到部门名称输入框
    if (nameInputRef.value) {
      nameInputRef.value.focus()
    }
  })
}

// 保存部门
async function handleSave() {
  if (!formRef.value) {
    return
  }

  try {
    // 表单验证
    await formRef.value.validate()

    dialogLoading.value = true

    let res: any
    if (isEdit.value) {
      // 编辑部门
      // console.log('编辑部门数据:', formData.value)
      // 判断parentId和id是否相同
      if (formData.value.parentId === formData.value.id) {
        throw new Error('不能又当爸爸又当儿子，请选择重新选择你的爸爸')
      }
      res = await apiSystem.editDepartment(formData.value)
    }
    else {
      // 新增部门
      const { id, ...addData } = formData.value
      res = await apiSystem.addDepartment(addData)
    }

    if (res.code === 0) {
      toast.success(isEdit.value ? '编辑成功' : '新增成功')
      dialogVisible.value = false
      getDepartmentList()
      getDepartmentTree() // 刷新部门树
    }
    else {
      toast.error(res.msg || (isEdit.value ? '编辑失败' : '新增失败'))
    }
  }
  catch (error: any) {
    if (error !== false) { // 表单验证失败不显示错误
      toast.error(error.message || (isEdit.value ? '编辑失败' : '新增失败'))
    }
  }
  finally {
    dialogLoading.value = false
  }
}

// 删除部门
async function handleDelete(row: Department) {
  try {
    await ElMessageBox.confirm(
      `确定要删除部门"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    const res: any = await apiSystem.deleteDepartment({ id: row.id })
    if (res.code === 0) {
      toast.success('删除成功')
      getDepartmentList()
      getDepartmentTree() // 刷新部门树
    }
    else {
      // 只弹出错误提示，不做退出登录等处理
      // toast.error(res.msg || '删除失败')
    }
  }
  catch (error: any) {
    if (error !== 'cancel') {
      // toast.error(error.message || '删除失败')
    }
  }
}

// 搜索
function handleSearch() {
  currentPage.value = 1
  getDepartmentList()
}

// 重置搜索
function handleReset() {
  searchForm.value = {
    name: '',
    parentId: '',
  }
  currentPage.value = 1
  getDepartmentList()
}

// 分页变化
function handlePageChange(page: number) {
  currentPage.value = page
  getDepartmentList()
}

// 页面大小变化
function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  getDepartmentList()
}

// 初始化
onMounted(() => {
  getDepartmentList()
  getDepartmentTree()
})
</script>

<template>
  <div>
    <FaPageHeader title="部门管理" description="管理系统部门结构" />

    <FaPageMain>
      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline>
        <el-form-item label="部门名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入部门名称"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item label="上级部门" prop="parentId">
          <DepartmentSelector
            v-model="searchForm.parentId"
            :department-data="departmentTreeData"
            placeholder="请选择上级部门"
          />
        </el-form-item>
        <el-form-item>
          <ElButton type="primary" @click="handleSearch">
            <template #icon>
              <FaIcon name="i-ep:search" />
            </template>
            搜索
          </ElButton>
          <ElButton class="ml-2" @click="handleReset">
            <template #icon>
              <FaIcon name="i-ep:refresh" />
            </template>
            重置
          </ElButton>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <div class="mb-4 flex items-center justify-between">
        <div class="flex items-center gap-2">
          <FaAuth :value="['/system/partment/save']">
            <FaButton
              type="primary"
              icon="i-ri:add-line"
              @click="openAddDialog"
            >
              新增部门
            </FaButton>
          </FaAuth>
          <FaButton
            type="success"
            icon="i-ri:refresh-line"
            @click="getDepartmentList"
          >
            刷新
          </FaButton>
        </div>
      </div>

      <!-- 部门列表 -->
      <el-table
        v-loading="loading"
        :data="departmentList"
        style="width: 100%;"
        border
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="name" label="部门名称" min-width="200">
          <template #default="scope">
            <span class="font-medium">{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="sort" label="排序" width="100" align="center">
          <template #default="scope">
            <el-tag size="small" type="info">
              {{ scope.row.sort || 0 }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="ctime" label="创建时间" width="180">
          <template #default="scope">
            <span class="text-gray-500">
              {{ new Date(scope.row.ctime).toLocaleString() }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="170" align="center">
          <template #default="scope">
            <FaAuth :value="['/system/partment/save']">
              <el-button
                text
                type="primary"
                size="small"
                @click="openEditDialog(scope.row)"
              >
                <template #icon>
                  <FaIcon name="i-ep:edit" />
                </template>
                编辑
              </el-button>
            </FaAuth>
            <FaAuth :value="['/system/partment/delete']">
              <el-button
                text
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
              >
                <template #icon>
                  <FaIcon name="i-ep:delete" />
                </template>
                删除
              </el-button>
            </FaAuth>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </FaPageMain>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-if="dialogVisible"
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
      @open="handleDialogOpen"
    >
      <el-form ref="formRef" :model="formData" label-width="100px" :rules="formRules">
        <el-form-item label="部门名称" prop="name">
          <el-input
            ref="nameInputRef"
            v-model="formData.name"
            placeholder="请输入部门名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="上级部门" prop="parentId">
          <DepartmentSelector
            v-model="formData.parentId"
            :department-data="departmentTreeData"
            :exclude-id="isEdit ? formData.id : ''"
            placeholder="请选择上级部门"
          />
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="formData.sort"
            :min="0"
            :max="999"
            placeholder="请输入排序值"
            style="width: 100%;"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="dialogLoading"
            @click="handleSave"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}

.department-tree-container {
  max-height: 300px;
  padding: 8px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.department-selector-footer {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding-top: 12px;
  margin-top: 12px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-tree-node__content) {
  height: 32px;
  line-height: 32px;
}

:deep(.el-tree-node__label) {
  font-size: 14px;
}
</style>
