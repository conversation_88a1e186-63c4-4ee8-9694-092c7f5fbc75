<script setup lang="ts">
// 类型导入
import type { Coupon, PaginationConfig } from './baseConfig/types'
import type { CouponListParams } from '@/api/modules/coupons/index.ts'
import { Download, Plus, Refresh } from '@element-plus/icons-vue'
import { ElButton, ElMessage, ElMessageBox, ElPagination } from 'element-plus'

import { onMounted, reactive, ref, toRaw } from 'vue'
// API导入
import { changeCouponStatus, getCouponList } from '@/api/modules/coupons'
// 基础组件

import CouponDetailDialog from '@/views/coupons/baseConfig/components/CouponDetailDialog.vue'
// 组件导入
import CouponSearchForm from '@/views/coupons/baseConfig/components/CouponSearchForm.vue'

import CouponTable from '@/views/coupons/baseConfig/components/CouponTable.vue'

// 限制条件对话框组件
import CouponRestrictionDialog from '@/views/coupons/restrictionConfig/CouponRestrictionDialog.vue'

// 常量导入
import { DEFAULT_PAGE_CONFIG } from './baseConfig/constants'
import CouponBaseConfig from './baseConfig/CouponConfigDialog.vue'
// 工具函数导入
import { adaptCouponList, downloadCSV, exportCouponsToCSV, formatAmount, getUseOnDesc } from './baseConfig/utils'

defineOptions({
  name: 'CouponManagement',
})

// ==================== 响应式数据 ====================

// 表格数据
const tableData = ref<Coupon[]>([])
const loading = ref(false)

// 显示主信息编辑窗口
const showBaseConfig = ref(false)
// 显示条件编辑窗口
const showConditionConfig = ref(false)

const dialogModel = ref<'add' | 'edit' | 'view'>('add')

// 当前编辑的优惠券信息
const editCouponId = ref<string>('')
const editCouponUseOn = ref<number>(0)
const editRestrictionData = ref<any>()

// 搜索参数
const searchParams = ref<CouponListParams>({
  id: undefined,
  name: undefined,
  useOn: undefined,
  couponType: undefined,
  useStatus: undefined,
  cinemaId: null,
  channelCode: null,
})

// 分页配置
const pagination = reactive<PaginationConfig>({ ...DEFAULT_PAGE_CONFIG })

// 搜索参数
// const searchParams = reactive<SearchFormData>({})

// 对话框状态
const detailDialogVisible = ref(false)
const currentCoupon = ref<Coupon | null>(null)

// 选中的行
const selectedRows = ref<Coupon[]>([])

// ==================== 生命周期 ====================

onMounted(() => {
  fetchCouponList()
})

// ==================== 数据获取 ====================

// 获取优惠券列表
async function fetchCouponList() {
  try {
    loading.value = true

    // 使用 toRaw 创建纯对象副本，避免响应式对象的循环引用
    const cleanSearchParams = toRaw(searchParams.value)
    const params: CouponListParams = {
      ...cleanSearchParams,
      page: pagination.page,
      size: pagination.size,
    }

    console.log('请求参数:', params)
    const response = await getCouponList(params)
    console.log('获取优惠券列表响应:', response)

    // 处理响应数据
    const data = response.data
    if (data && typeof data === 'object') {
      // 兼容多种数据格式并适配数据类型
      const rawList = (data as any).content || (data as any).result || (data as any).records || (data as any).list || (data as any).rows || []
      tableData.value = adaptCouponList(rawList)
      pagination.total = (data as any).total || 0
    }
    else {
      tableData.value = []
      pagination.total = 0
    }
  }
  catch (error) {
    console.error('获取优惠券列表失败:', error)
    ElMessage.error('获取优惠券列表失败')
    tableData.value = []
    pagination.total = 0
  }
  finally {
    loading.value = false
  }
}

// ==================== 事件处理 ====================

// 搜索
function handleSearch(params: CouponListParams) {
  Object.assign(searchParams.value, params)
  pagination.page = 1
  fetchCouponList()
}

// 重置搜索
function handleReset() {
  // 重置搜索参数为初始值
  searchParams.value = {
    id: '',
    name: '',
    useOn: undefined,
    couponType: undefined,
    useStatus: undefined,
    cinemaId: '',
    channelCode: '',
  }
  pagination.page = 1
  fetchCouponList()
}

// 分页变化
function handlePageChange(page: number) {
  pagination.page = page
  fetchCouponList()
}

function handleSizeChange(size: number) {
  pagination.size = size
  pagination.page = 1
  fetchCouponList()
}

// 查看优惠券详情
function handleViewDetail(coupon: Coupon) {
  currentCoupon.value = coupon
  detailDialogVisible.value = true
}

// 编辑优惠券 - 默认编辑基础信息
function handleEdit(coupon: Coupon) {
  editCouponId.value = coupon.id
  showBaseConfig.value = true
  dialogModel.value = 'edit'
  console.log('编辑优惠券:', coupon)
}

// 编辑优惠券（带模式参数）
function handleEditWithMode(coupon: Coupon, mode: 'base' | 'condition') {
  switch (mode) {
    case 'base':
      handleEdit(coupon)
      break
    case 'condition':
      handleEditCondition(coupon)
      break
    default:
      break
  }
}

/**
 * 编辑优惠券条件
 */
async function handleEditCondition(coupon: Coupon) {
  console.log('编辑优惠券条件:', coupon)

  try {
    // 设置当前编辑的优惠券信息
    editCouponId.value = coupon.id
    editCouponUseOn.value = coupon.useOn || 0

    // 这里可以调用API获取现有的限制条件数据
    // const response = await getRestrictionDetail({ couponId: coupon.id, useOn: coupon.useOn })
    // editRestrictionData.value = response.data

    // 暂时使用模拟数据，实际使用时替换为API调用
    editRestrictionData.value = {
      couponId: coupon.id,
      // 根据优惠券类型设置对应的限制数据
      ...(coupon.useOn === 0 && {
        ticketRestriction: {
          couponId: coupon.id,
          id: `restriction_${coupon.id}`,
          cinemaRestriction: {
            cinemaScope: 0,
            cinemas: [],
          },
          filmRestriction: {
            filmScope: 0,
            films: [],
          },
          periodRestriction: {
            periodScope: 0,
            periods: [],
          },
          filmVersionRestriction: {
            versionScope: 0,
            versions: [],
          },
        },
      }),
      ...(coupon.useOn === 1 && {
        goodsRestriction: {
          goodsScope: 0,
          cinemas: [],
          goodsTypeIds: [],
          goods: [],
        },
      }),
      ...(coupon.useOn === 2 && {
        showRestriction: {
          showScope: 0,
          shows: [],
        },
      }),
      ...(coupon.useOn === 3 && {
        exhibitionRestriction: {
          exhibitScope: 0,
          exhibitHouses: [],
          exhibits: [],
        },
      }),
    }

    // 打开条件编辑对话框
    showConditionConfig.value = true
  }
  catch (error) {
    console.error('获取限制条件失败:', error)
    ElMessage.error('获取限制条件失败')
  }
}

/**
 * 处理条件提交
 */
async function handleConditionSubmit(data: any) {
  console.log('提交限制条件数据:', data)

  try {
    // 这里调用更新限制条件的API
    // const response = await updateRestrictionAPI(data)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('限制条件更新成功！')
    showConditionConfig.value = false

    // 刷新列表数据
    await fetchCouponList()
  }
  catch (error) {
    console.error('更新限制条件失败:', error)
    ElMessage.error('更新限制条件失败')
  }
}

/**
 * 处理条件创建成功
 */
function handleConditionSuccess(data: any) {
  console.log('限制条件创建成功:', data)
  ElMessage.success(`限制条件创建成功！ID: ${data.id}`)
  showConditionConfig.value = false

  // 刷新列表数据
  fetchCouponList()
}

/**
 * 处理条件编辑取消
 */
function handleConditionCancel() {
  console.log('取消编辑限制条件')
  showConditionConfig.value = false
}

// 删除优惠券
async function handleDelete(coupon: Coupon) {
  try {
    await ElMessageBox.confirm(
      `确定要删除优惠券"${coupon.name}"吗？删除后无法恢复，请谨慎操作。`,
      '删除确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false,
      },
    )

    // 这里应该调用删除API
    ElMessage.info('删除功能开发中...')
    console.log('删除优惠券:', coupon)
    // 删除成功后刷新列表
    // await deleteCoupon(coupon.id)
    // ElMessage.success('删除成功')
    // fetchCouponList()
  }
  catch {
    // 用户取消删除
  }
}

// 状态切换
async function handleStatusChange(coupon: Coupon, newValue: boolean) {
  const newStatus = newValue ? 1 : 0
  const statusText = newStatus === 1 ? '启用' : '禁用'

  // 如果状态没有变化，直接返回
  if (Number(coupon.useStatus) === newStatus) {
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要${statusText}优惠券"${coupon.name}"吗？`,
      `${statusText}确认`,
      {
        confirmButtonText: `确认${statusText}`,
        cancelButtonText: '取消',
        type: 'info',
      },
    )

    const response = await changeCouponStatus({
      id: coupon.id,
      useStatus: newStatus,
    })

    if (response.data.code === 0) {
      coupon.useStatus = newStatus
      ElMessage.success(`${statusText}成功`)
      fetchCouponList() // 刷新列表
    }
    else {
      ElMessage.error(response.data.msg || `${statusText}失败`)
    }
  }
  catch (error: any) {
    if (error !== 'cancel') {
      console.error('状态更新失败:', error)
      ElMessage.error('状态更新失败')
    }
    // 如果用户取消或操作失败，重新获取数据确保状态一致性
    fetchCouponList()
  }
}

// 绑定优惠券
function handleBind(coupon: Coupon) {
  ElMessage.info('绑定功能开发中...')
  console.log('绑定优惠券:', coupon)
}

// 查看优惠券码
function handleViewCodes(coupon: Coupon) {
  ElMessage.info('查看券码功能开发中...')
  console.log('查看优惠券码:', coupon)
}

// 详情弹窗中的编辑操作
function handleDetailEdit(coupon: Coupon) {
  detailDialogVisible.value = false
  ElMessage.info('编辑功能开发中...')
  console.log('编辑优惠券:', coupon)
}

// 详情弹窗中的导出操作
function handleDetailExport(coupon: Coupon) {
  const csvContent = exportCouponsToCSV([coupon])
  const filename = `coupon_${coupon.id}_${new Date().toISOString().slice(0, 10)}.csv`
  downloadCSV(csvContent, filename)
  ElMessage.success('导出成功')
}

// 详情弹窗中的分享操作
function handleDetailShare(coupon: Coupon) {
  // 复制优惠券信息到剪贴板
  const shareText = `优惠券：${coupon.name}\n用户服务费：${formatAmount(coupon.userServiceFee)}\n适用类型：${getUseOnDesc(coupon.useOn)}\n优惠券ID：${coupon.id}`

  if (navigator.clipboard) {
    navigator.clipboard.writeText(shareText).then(() => {
      ElMessage.success('优惠券信息已复制到剪贴板')
    }).catch(() => {
      ElMessage.error('复制失败，请手动复制')
    })
  }
  else {
    ElMessage.info('分享功能开发中...')
  }
}

// 表格选择变化
function handleSelectionChange(selection: Coupon[]) {
  selectedRows.value = selection
}

// 新建优惠券
function handleCreate() {
  // ElMessage.info('新建功能开发中...')
  showBaseConfig.value = true
  dialogModel.value = 'add'
}

// 批量导出
function handleBatchExport() {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要导出的优惠券')
    return
  }

  const csvContent = exportCouponsToCSV(selectedRows.value)
  const filename = `coupons_batch_${new Date().toISOString().slice(0, 10)}.csv`
  downloadCSV(csvContent, filename)
  ElMessage.success('批量导出成功')
}

// 导出全部
async function handleExportAll() {
  try {
    const csvContent = exportCouponsToCSV(tableData.value)
    const filename = `coupons_all_${new Date().toISOString().slice(0, 10)}.csv`
    downloadCSV(csvContent, filename)
    ElMessage.success('导出成功')
  }
  catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 刷新
function handleRefresh() {
  fetchCouponList()
}
</script>

<template>
  <div class="coupon-management">
    <fa-page-header title="优惠券管理" description="管理系统中的所有优惠券，包括查看优惠券详情、编辑优惠券等操作">
      <template #default>
        <!-- 操作按钮 -->
        <div class="flex items-center gap-2">
          <ElButton type="primary" :icon="Plus" @click="handleCreate">
            新建优惠券
          </ElButton>
          <ElButton
            :icon="Download"
            :disabled="selectedRows.length === 0"
            @click="handleBatchExport"
          >
            批量导出 ({{ selectedRows.length }})
          </ElButton>
          <ElButton :icon="Refresh" @click="handleRefresh">
            刷新
          </ElButton>
        </div>
      </template>
    </fa-page-header>
    <fa-page-main>
      <!-- 搜索表单 -->
      {{ searchParams }}
      <CouponSearchForm
        v-model="searchParams"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
        @export="handleExportAll"
      />

      <!-- 数据表格 -->
      <CouponTable
        :data="tableData"
        :loading="loading"
        @view-detail="handleViewDetail"
        @edit="handleEdit"
        @delete="handleDelete"
        @status-change="handleStatusChange"
        @bind="handleBind"
        @view-codes="handleViewCodes"
        @selection-change="handleSelectionChange"
      />

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <ElPagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </fa-page-main>

    <!-- 优惠券详情对话框 -->
    <CouponDetailDialog
      v-model="detailDialogVisible"
      :coupon="currentCoupon"
      @edit="handleDetailEdit"
      @export="handleDetailExport"
      @share="handleDetailShare"
    />

    <!-- 优惠券配置对话框 -->
    <CouponBaseConfig
      v-model="showBaseConfig"
      :page-mode="dialogModel"
      :coupon-id="editCouponId"
    />

    <!-- 优惠券限制条件对话框 -->
    <CouponRestrictionDialog
      v-model="showConditionConfig"
      :coupon-id="editCouponId"
      :use-on="editCouponUseOn"
      page-mode="edit"
      title="编辑优惠券限制条件"
      :restriction-data="editRestrictionData"
      @submit="handleConditionSubmit"
      @success="handleConditionSuccess"
      @cancel="handleConditionCancel"
      @close="handleConditionCancel"
    />
  </div>
</template>

<style scoped>
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
