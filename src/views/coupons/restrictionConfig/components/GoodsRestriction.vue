<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { ElFormItem, ElRadioGroup, ElRadio, ElSelect, ElOption, ElText, ElTag } from 'element-plus'

// 类型导入
import type { GoodsRestriction, Cinema, Goods } from '../types'

// 常量导入
import { SCOPE_OPTIONS } from '../constants'

// API导入
import { getCinemaList, getGoodsList } from '../api'

defineOptions({
  name: 'GoodsRestriction',
})

// ==================== Props & Emits ====================

interface Props {
  modelValue?: GoodsRestriction
}

interface Emits {
  (e: 'update:modelValue', value: GoodsRestriction): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    goodsScope: 0,
    cinemas: [],
    goodsTypeIds: [],
    goods: []
  })
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 选项数据
const cinemaOptions = ref<Cinema[]>([])
const goodsOptions = ref<Goods[]>([])

// 卖品分类选项（模拟数据）
const goodsTypeOptions = ref([
  { id: 1, name: '饮料' },
  { id: 2, name: '爆米花' },
  { id: 3, name: '零食' },
  { id: 4, name: '套餐' },
])

// 加载状态
const loading = ref({
  cinema: false,
  goods: false,
})

// 表单数据计算属性
const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// ==================== 业务逻辑 ====================

/**
 * 加载影院列表
 */
async function loadCinemaList() {
  try {
    loading.value.cinema = true
    const response = await getCinemaList()
    cinemaOptions.value = response.data
  } catch (error) {
    console.error('加载影院列表失败:', error)
  } finally {
    loading.value.cinema = false
  }
}

/**
 * 加载卖品列表
 */
async function loadGoodsList() {
  try {
    loading.value.goods = true
    const response = await getGoodsList()
    goodsOptions.value = response.data
  } catch (error) {
    console.error('加载卖品列表失败:', error)
  } finally {
    loading.value.goods = false
  }
}

/**
 * 处理卖品范围变化
 */
function handleGoodsScopeChange() {
  if (formData.value.goodsScope === 0) {
    formData.value.cinemas = []
    formData.value.goodsTypeIds = []
    formData.value.goods = []
  }
}

/**
 * 获取卖品分类名称
 */
function getGoodsTypeName(id: number): string {
  const type = goodsTypeOptions.value.find(item => item.id === id)
  return type?.name || '未知分类'
}

// ==================== 生命周期 ====================

onMounted(() => {
  loadCinemaList()
  loadGoodsList()
})
</script>

<template>
  <div class="goods-restriction">
    <div class="section-title">
      <h3>卖品限制条件</h3>
      <p>设置卖品优惠券的使用限制条件</p>
    </div>

    <div class="restriction-sections">
      <!-- 卖品限制 -->
      <div class="restriction-section">
        <div class="section-header">
          <h4>卖品限制</h4>
        </div>

        <ElFormItem label="卖品范围">
          <ElRadioGroup
            v-model="formData.goodsScope"
            @change="handleGoodsScopeChange"
          >
            <ElRadio
              v-for="option in SCOPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <!-- 指定或排除卖品时的配置 -->
        <template v-if="formData.goodsScope !== 0">
          <!-- 影院选择 -->
          <ElFormItem label="适用影院">
            <ElSelect
              v-model="formData.cinemas"
              placeholder="请选择影院（不选择表示全部影院）"
              multiple
              collapse-tags
              collapse-tags-tooltip
              filterable
              clearable
              :loading="loading.cinema"
              style="width: 100%"
              value-key="cinemaId"
            >
              <ElOption
                v-for="cinema in cinemaOptions"
                :key="cinema.cinemaId"
                :label="cinema.cinemaName"
                :value="cinema"
              />
            </ElSelect>
          </ElFormItem>

          <!-- 卖品分类选择 -->
          <ElFormItem label="卖品分类">
            <ElSelect
              v-model="formData.goodsTypeIds"
              placeholder="请选择卖品分类（不选择表示全部分类）"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              style="width: 100%"
            >
              <ElOption
                v-for="type in goodsTypeOptions"
                :key="type.id"
                :label="type.name"
                :value="type.id"
              />
            </ElSelect>
          </ElFormItem>

          <!-- 具体卖品选择 -->
          <ElFormItem label="具体卖品">
            <ElSelect
              v-model="formData.goods"
              placeholder="请选择具体卖品（不选择表示按分类限制）"
              multiple
              collapse-tags
              collapse-tags-tooltip
              filterable
              clearable
              :loading="loading.goods"
              style="width: 100%"
              value-key="goodsId"
            >
              <ElOption
                v-for="goods in goodsOptions"
                :key="goods.goodsId"
                :label="`${goods.goodsName} - ${goods.cinemaName}`"
                :value="goods"
              >
                <div class="goods-option">
                  <div class="goods-name">{{ goods.goodsName }}</div>
                  <div class="goods-cinema">{{ goods.cinemaName }}</div>
                </div>
              </ElOption>
            </ElSelect>
          </ElFormItem>
        </template>

        <!-- 配置总结 -->
        <div v-if="formData.goodsScope !== 0" class="restriction-summary">
          <ElText type="info" size="small">限制条件总结：</ElText>
          <div class="summary-content">
            <div class="summary-item">
              <span class="label">范围类型：</span>
              <ElTag :type="formData.goodsScope === 1 ? 'success' : 'danger'">
                {{ formData.goodsScope === 1 ? '指定' : '排除' }}
              </ElTag>
            </div>

            <div v-if="formData.cinemas.length > 0" class="summary-item">
              <span class="label">适用影院：</span>
              <div class="tag-list">
                <ElTag
                  v-for="cinema in formData.cinemas"
                  :key="cinema.cinemaId"
                  size="small"
                >
                  {{ cinema.cinemaName }}
                </ElTag>
              </div>
            </div>

            <div v-if="formData.goodsTypeIds.length > 0" class="summary-item">
              <span class="label">卖品分类：</span>
              <div class="tag-list">
                <ElTag
                  v-for="typeId in formData.goodsTypeIds"
                  :key="typeId"
                  size="small"
                  type="warning"
                >
                  {{ getGoodsTypeName(typeId) }}
                </ElTag>
              </div>
            </div>

            <div v-if="formData.goods.length > 0" class="summary-item">
              <span class="label">具体卖品：</span>
              <div class="tag-list">
                <ElTag
                  v-for="goods in formData.goods"
                  :key="goods.goodsId"
                  size="small"
                  type="info"
                >
                  {{ goods.goodsName }}
                </ElTag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.goods-restriction {
  width: 100%;
}

.section-title {
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--el-color-primary);
}

.section-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.section-title p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.restriction-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.restriction-section {
  padding: 20px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
}

.section-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 表单项样式 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* 单选按钮组样式 */
:deep(.el-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

:deep(.el-radio) {
  margin-right: 0;
}

/* 卖品选项样式 */
.goods-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.goods-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.goods-cinema {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

/* 限制条件总结样式 */
.restriction-summary {
  margin-top: 16px;
  padding: 12px;
  background: var(--el-fill-color-light);
  border-radius: 6px;
}

.summary-content {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.summary-item .label {
  min-width: 80px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .restriction-section {
    padding: 16px;
  }

  .summary-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .summary-item .label {
    min-width: auto;
  }

  :deep(.el-radio-group) {
    flex-direction: column;
    gap: 8px;
  }
}

/* 动画效果 */
.restriction-section-animation {
  animation: fade-in-up 0.3s ease-out;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
