<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { ElFormItem, ElRadioGroup, ElRadio, ElSelect, ElOption, ElText } from 'element-plus'

// 类型导入
import type { ExhibitionRestriction, Exhibition, ExhibitHouse } from '../types'

// 常量导入
import { SCOPE_OPTIONS } from '../constants'

// API导入
import { getExhibitionList } from '../api'

defineOptions({
  name: 'ExhibitionRestriction',
})

// ==================== Props & Emits ====================

interface Props {
  modelValue?: ExhibitionRestriction
}

interface Emits {
  (e: 'update:modelValue', value: ExhibitionRestriction): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    exhibitScope: 0,
    exhibitHouses: [],
    exhibits: []
  })
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 选项数据
const exhibitionOptions = ref<Exhibition[]>([])

// 展览场馆选项（模拟数据）
const exhibitHouseOptions = ref<ExhibitHouse[]>([
  {
    houseId: 'house_001',
    halls: [
      { hallId: 'hall_001', hallName: 'A展厅' },
      { hallId: 'hall_002', hallName: 'B展厅' },
      { hallId: 'hall_003', hallName: 'C展厅' },
    ]
  },
  {
    houseId: 'house_002',
    halls: [
      { hallId: 'hall_004', hallName: '1号展厅' },
      { hallId: 'hall_005', hallName: '2号展厅' },
    ]
  }
])

// 加载状态
const loading = ref(false)

// 表单数据计算属性
const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// ==================== 业务逻辑 ====================

/**
 * 加载展览列表
 */
async function loadExhibitionList() {
  try {
    loading.value = true
    const response = await getExhibitionList()
    exhibitionOptions.value = response.data
  } catch (error) {
    console.error('加载展览列表失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 处理展览范围变化
 */
function handleExhibitScopeChange() {
  if (formData.value.exhibitScope === 0) {
    formData.value.exhibitHouses = []
    formData.value.exhibits = []
  }
}

// ==================== 生命周期 ====================

onMounted(() => {
  loadExhibitionList()
})
</script>

<template>
  <div class="exhibition-restriction">
    <div class="section-title">
      <h3>展览限制条件</h3>
      <p>设置展览优惠券的使用限制条件</p>
    </div>

    <div class="restriction-sections">
      <!-- 展览限制 -->
      <div class="restriction-section">
        <div class="section-header">
          <h4>展览限制</h4>
        </div>

        <ElFormItem label="展览范围">
          <ElRadioGroup
            v-model="formData.exhibitScope"
            @change="handleExhibitScopeChange"
          >
            <ElRadio
              v-for="option in SCOPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <!-- 指定或排除展览时的配置 -->
        <template v-if="formData.exhibitScope !== 0">
          <!-- 展览场馆选择 -->
          <ElFormItem label="展览场馆">
            <ElSelect
              v-model="formData.exhibitHouses"
              placeholder="请选择展览场馆（不选择表示全部场馆）"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              style="width: 100%"
              value-key="houseId"
            >
              <ElOption
                v-for="house in exhibitHouseOptions"
                :key="house.houseId"
                :label="`场馆${house.houseId} (${house.halls?.length || 0}个展厅)`"
                :value="house"
              />
            </ElSelect>
          </ElFormItem>

          <!-- 具体展览选择 -->
          <ElFormItem label="具体展览">
            <ElSelect
              v-model="formData.exhibits"
              placeholder="请选择具体展览"
              multiple
              collapse-tags
              collapse-tags-tooltip
              filterable
              :loading="loading"
              style="width: 100%"
              value-key="exhibitId"
            >
              <ElOption
                v-for="exhibit in exhibitionOptions"
                :key="exhibit.exhibitId"
                :label="exhibit.exhibitName"
                :value="exhibit"
              />
            </ElSelect>
          </ElFormItem>
        </template>

        <!-- 已选展览展示 -->
        <div v-if="formData.exhibits.length > 0" class="selected-exhibitions">
          <ElText type="info" size="small">已选择展览：</ElText>
          <div class="exhibition-list">
            <div
              v-for="exhibit in formData.exhibits"
              :key="exhibit.exhibitId"
              class="exhibition-item"
            >
              <div class="exhibition-info">
                <div class="exhibition-name">{{ exhibit.exhibitName }}</div>
                <div class="exhibition-id">ID: {{ exhibit.exhibitId }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 已选场馆展示 -->
        <div v-if="formData.exhibitHouses.length > 0" class="selected-houses">
          <ElText type="info" size="small">已选择场馆：</ElText>
          <div class="house-list">
            <div
              v-for="house in formData.exhibitHouses"
              :key="house.houseId"
              class="house-item"
            >
              <div class="house-info">
                <div class="house-name">场馆 {{ house.houseId }}</div>
                <div v-if="house.halls && house.halls.length > 0" class="house-halls">
                  <span class="halls-label">展厅：</span>
                  <span
                    v-for="(hall, index) in house.halls"
                    :key="hall.hallId"
                    class="hall-name"
                  >
                    {{ hall.hallName }}{{ index < house.halls.length - 1 ? '、' : '' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.exhibition-restriction {
  width: 100%;
}

.section-title {
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--el-color-primary);
}

.section-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.section-title p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.restriction-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.restriction-section {
  padding: 20px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
}

.section-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 表单项样式 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* 单选按钮组样式 */
:deep(.el-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

:deep(.el-radio) {
  margin-right: 0;
}

/* 已选展览样式 */
.selected-exhibitions {
  margin-top: 16px;
  padding: 12px;
  background: var(--el-fill-color-light);
  border-radius: 6px;
}

.exhibition-list {
  margin-top: 8px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.exhibition-item {
  padding: 12px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
}

.exhibition-info .exhibition-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.exhibition-info .exhibition-id {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

/* 已选场馆样式 */
.selected-houses {
  margin-top: 16px;
  padding: 12px;
  background: var(--el-fill-color-light);
  border-radius: 6px;
}

.house-list {
  margin-top: 8px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 12px;
}

.house-item {
  padding: 12px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
}

.house-info .house-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.house-info .house-halls {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.house-info .halls-label {
  font-weight: 500;
}

.house-info .hall-name {
  color: var(--el-color-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .restriction-section {
    padding: 16px;
  }

  .exhibition-list,
  .house-list {
    grid-template-columns: 1fr;
  }

  :deep(.el-radio-group) {
    flex-direction: column;
    gap: 8px;
  }
}

/* 动画效果 */
.restriction-section-animation {
  animation: fade-in-up 0.3s ease-out;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
