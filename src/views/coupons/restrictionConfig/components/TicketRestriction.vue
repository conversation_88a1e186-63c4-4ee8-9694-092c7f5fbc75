<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { ElFormItem, ElRadioGroup, ElRadio, ElSelect, ElOption, ElButton, ElText, ElIcon, ElTag, ElTimePicker, ElCheckboxGroup, ElCheckbox } from 'element-plus'
import { Plus, Delete, InfoFilled } from '@element-plus/icons-vue'

// 类型导入
import type { TicketRestriction, Cinema, Film, Period, FilmVersion } from '../types'

// 常量导入
import {
  SCOPE_OPTIONS,
  FILM_SCOPE_OPTIONS,
  PERIOD_SCOPE_OPTIONS,
  VERSION_SCOPE_OPTIONS,
  WEEK_DAY_OPTIONS,
  formatTimeNumber,
  parseTimeString
} from '../constants'

// API导入
import { getCinemaList, getFilmList, getFilmVersionList } from '../api'

defineOptions({
  name: 'TicketRestriction',
})

// ==================== Props & Emits ====================

interface Props {
  modelValue?: TicketRestriction
}

interface Emits {
  (e: 'update:modelValue', value: TicketRestriction): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    couponId: '',
    id: '',
    cinemaRestriction: {
      cinemaScope: 0,
      cinemas: []
    },
    filmRestriction: {
      filmScope: 0,
      films: []
    },
    periodRestriction: {
      periodScope: 0,
      periods: []
    },
    filmVersionRestriction: {
      versionScope: 0,
      versions: []
    }
  })
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 选项数据
const cinemaOptions = ref<Cinema[]>([])
const filmOptions = ref<Film[]>([])
const versionOptions = ref<FilmVersion[]>([])

// 加载状态
const loading = ref({
  cinema: false,
  film: false,
  version: false,
})

// 表单数据计算属性
const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// ==================== 业务逻辑 ====================

/**
 * 加载影院列表
 */
async function loadCinemaList() {
  try {
    loading.value.cinema = true
    const response = await getCinemaList()
    cinemaOptions.value = response.data
  } catch (error) {
    console.error('加载影院列表失败:', error)
  } finally {
    loading.value.cinema = false
  }
}

/**
 * 加载影片列表
 */
async function loadFilmList() {
  try {
    loading.value.film = true
    const response = await getFilmList()
    filmOptions.value = response.data
  } catch (error) {
    console.error('加载影片列表失败:', error)
  } finally {
    loading.value.film = false
  }
}

/**
 * 加载影片版本列表
 */
async function loadVersionList() {
  try {
    loading.value.version = true
    const response = await getFilmVersionList()
    versionOptions.value = response.data
  } catch (error) {
    console.error('加载影片版本列表失败:', error)
  } finally {
    loading.value.version = false
  }
}

/**
 * 添加时间段
 */
function addPeriod() {
  const newPeriod: Period = {
    num: formData.value.periodRestriction.periods.length + 1,
    day: 1, // 默认周一
    start: 1800, // 默认18:00
    end: 2200, // 默认22:00
  }

  formData.value.periodRestriction.periods.push(newPeriod)
}

/**
 * 删除时间段
 */
function removePeriod(index: number) {
  formData.value.periodRestriction.periods.splice(index, 1)
  // 重新编号
  formData.value.periodRestriction.periods.forEach((period, idx) => {
    period.num = idx + 1
  })
}

/**
 * 处理影院范围变化
 */
function handleCinemaScopeChange() {
  if (formData.value.cinemaRestriction.cinemaScope === 0) {
    formData.value.cinemaRestriction.cinemas = []
  }
}

/**
 * 处理影院选择变化
 */
function handleCinemaSelectionChange() {
  // 为每个选中的影院初始化selectedHalls属性
  formData.value.cinemaRestriction.cinemas.forEach(cinema => {
    if (!cinema.selectedHalls) {
      cinema.selectedHalls = []
    }
  })
}

/**
 * 全选影厅
 */
function selectAllHalls(cinema: any) {
  if (cinema.halls && cinema.halls.length > 0) {
    cinema.selectedHalls = cinema.halls.map((hall: any) => hall.hallId)
  }
}

/**
 * 处理影片范围变化
 */
function handleFilmScopeChange() {
  if (formData.value.filmRestriction.filmScope === 0 || formData.value.filmRestriction.filmScope === 2) {
    formData.value.filmRestriction.films = []
  }
}

/**
 * 处理时段范围变化
 */
function handlePeriodScopeChange() {
  if (formData.value.periodRestriction.periodScope === 0) {
    formData.value.periodRestriction.periods = []
  }
}

/**
 * 处理版本范围变化
 */
function handleVersionScopeChange() {
  if (formData.value.filmVersionRestriction.versionScope === 0) {
    formData.value.filmVersionRestriction.versions = []
  }
}

/**
 * 格式化时间显示
 */
function formatPeriodTime(timeNum: number): string {
  return formatTimeNumber(timeNum)
}

/**
 * 更新时间段时间
 */
function updatePeriodTime(index: number, field: 'start' | 'end', timeStr: string) {
  const timeNum = parseTimeString(timeStr)
  formData.value.periodRestriction.periods[index][field] = timeNum
}

// ==================== 生命周期 ====================

onMounted(() => {
  loadCinemaList()
  loadFilmList()
  loadVersionList()
})
</script>

<template>
  <div class="ticket-restriction">
    <div class="section-title">
      <h3>影票限制条件</h3>
      <p>设置影票优惠券的使用限制条件</p>
    </div>

    <div class="restriction-sections">
      <!-- 影院限制 -->
      <div class="restriction-section">
        <div class="section-header">
          <h4>影院限制</h4>
        </div>

        <ElFormItem label="影院范围">
          <ElRadioGroup
            v-model="formData.cinemaRestriction.cinemaScope"
            @change="handleCinemaScopeChange"
          >
            <ElRadio
              v-for="option in SCOPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <ElFormItem
          v-if="formData.cinemaRestriction.cinemaScope !== 0"
          label="选择影院"
        >
          <ElSelect
            v-model="formData.cinemaRestriction.cinemas"
            placeholder="请选择影院"
            multiple
            collapse-tags
            collapse-tags-tooltip
            filterable
            :loading="loading.cinema"
            style="width: 100%"
            value-key="cinemaId"
            @change="handleCinemaSelectionChange"
          >
            <ElOption
              v-for="cinema in cinemaOptions"
              :key="cinema.cinemaId"
              :label="cinema.cinemaName"
              :value="cinema"
            />
          </ElSelect>
        </ElFormItem>

        <!-- 影厅选择 -->
        <div v-if="formData.cinemaRestriction.cinemas.length > 0" class="hall-selection">
          <ElText type="info" size="small" class="hall-selection-title">
            <ElIcon><InfoFilled /></ElIcon>
            影厅选择（可选择具体影厅进行更精确的限制）
          </ElText>

          <div class="cinema-halls">
            <div
              v-for="cinema in formData.cinemaRestriction.cinemas"
              :key="cinema.cinemaId"
              class="cinema-hall-item"
            >
              <div class="cinema-hall-header">
                <h5>{{ cinema.cinemaName }}</h5>
                <ElButton
                  v-if="cinema.halls && cinema.halls.length > 0"
                  size="small"
                  type="primary"
                  text
                  @click="selectAllHalls(cinema)"
                >
                  全选影厅
                </ElButton>
              </div>

              <div v-if="cinema.halls && cinema.halls.length > 0" class="hall-checkboxes">
                <ElCheckboxGroup v-model="cinema.selectedHalls">
                  <ElCheckbox
                    v-for="hall in cinema.halls"
                    :key="hall.hallId"
                    :value="hall.hallId"
                    :label="hall.hallId"
                  >
                    {{ hall.hallName }}
                  </ElCheckbox>
                </ElCheckboxGroup>
              </div>

              <div v-else class="no-halls">
                <ElText type="info" size="small">该影院暂无影厅信息</ElText>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 影片限制 -->
      <div class="restriction-section">
        <div class="section-header">
          <h4>影片限制</h4>
        </div>

        <ElFormItem label="影片范围">
          <ElRadioGroup
            v-model="formData.filmRestriction.filmScope"
            @change="handleFilmScopeChange"
          >
            <ElRadio
              v-for="option in FILM_SCOPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <ElFormItem
          v-if="formData.filmRestriction.filmScope === 1"
          label="选择影片"
        >
          <ElSelect
            v-model="formData.filmRestriction.films"
            placeholder="请选择影片"
            multiple
            collapse-tags
            collapse-tags-tooltip
            filterable
            :loading="loading.film"
            style="width: 100%"
            value-key="filmId"
          >
            <ElOption
              v-for="film in filmOptions"
              :key="film.filmId"
              :label="film.filmName"
              :value="film"
            />
          </ElSelect>
        </ElFormItem>
      </div>

      <!-- 时段限制 -->
      <div class="restriction-section">
        <div class="section-header">
          <h4>时段限制</h4>
        </div>

        <ElFormItem label="时段范围">
          <ElRadioGroup
            v-model="formData.periodRestriction.periodScope"
            @change="handlePeriodScopeChange"
          >
            <ElRadio
              v-for="option in PERIOD_SCOPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <div v-if="formData.periodRestriction.periodScope === 1" class="period-config">
          <div class="period-header">
            <ElText>时段配置</ElText>
            <ElButton type="primary" size="small" :icon="Plus" @click="addPeriod">
              添加时段
            </ElButton>
          </div>

          <div
            v-for="(period, index) in formData.periodRestriction.periods"
            :key="index"
            class="period-item"
          >
            <div class="period-item-header">
              <ElText>时段 {{ period.num }}</ElText>
              <ElButton
                type="danger"
                size="small"
                text
                :icon="Delete"
                @click="removePeriod(index)"
              >
                删除
              </ElButton>
            </div>

            <div class="period-item-content">
              <ElFormItem label="星期">
                <ElSelect v-model="period.day" style="width: 120px">
                  <ElOption
                    v-for="day in WEEK_DAY_OPTIONS"
                    :key="day.value"
                    :label="day.label"
                    :value="day.value"
                  />
                </ElSelect>
              </ElFormItem>

              <ElFormItem label="开始时间">
                <ElTimePicker
                  :model-value="formatPeriodTime(period.start)"
                  format="HH:mm"
                  value-format="HH:mm"
                  @update:model-value="(val) => updatePeriodTime(index, 'start', val)"
                />
              </ElFormItem>

              <ElFormItem label="结束时间">
                <ElTimePicker
                  :model-value="formatPeriodTime(period.end)"
                  format="HH:mm"
                  value-format="HH:mm"
                  @update:model-value="(val) => updatePeriodTime(index, 'end', val)"
                />
              </ElFormItem>
            </div>
          </div>
        </div>
      </div>

      <!-- 影片版本限制 -->
      <div class="restriction-section">
        <div class="section-header">
          <h4>影片版本限制</h4>
        </div>

        <ElFormItem label="版本范围">
          <ElRadioGroup
            v-model="formData.filmVersionRestriction.versionScope"
            @change="handleVersionScopeChange"
          >
            <ElRadio
              v-for="option in VERSION_SCOPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <ElFormItem
          v-if="formData.filmVersionRestriction.versionScope === 1"
          label="选择版本"
        >
          <ElSelect
            v-model="formData.filmVersionRestriction.versions"
            placeholder="请选择影片版本"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :loading="loading.version"
            style="width: 100%"
            value-key="versionId"
          >
            <ElOption
              v-for="version in versionOptions"
              :key="version.versionId"
              :label="version.filmVersion"
              :value="version"
            />
          </ElSelect>
        </ElFormItem>
      </div>
    </div>
  </div>
</template>

<style scoped>
.ticket-restriction {
  width: 100%;
}

.section-title {
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--el-color-primary);
}

.section-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.section-title p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.restriction-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.restriction-section {
  padding: 20px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
}

.section-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 表单项样式 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* 单选按钮组样式 */
:deep(.el-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

:deep(.el-radio) {
  margin-right: 0;
}

/* 复选框组样式 */
:deep(.el-checkbox-group) {
  display: contents;
}

:deep(.el-checkbox) {
  margin-right: 0;
  margin-bottom: 0;
  white-space: nowrap;
}

:deep(.el-checkbox__label) {
  font-size: 13px;
  padding-left: 6px;
}

/* 影厅选择样式 */
.hall-selection {
  margin-top: 16px;
  padding: 16px;
  background: var(--el-fill-color-light);
  border-radius: 6px;
}

.hall-selection-title {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 16px;
  font-weight: 500;
}

.cinema-halls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.cinema-hall-item {
  padding: 16px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
}

.cinema-hall-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.cinema-hall-header h5 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.hall-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 8px;
}

.no-halls {
  padding: 12px;
  text-align: center;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

/* 时段配置样式 */
.period-config {
  margin-top: 16px;
  padding: 16px;
  background: var(--el-fill-color-light);
  border-radius: 6px;
}

.period-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.period-item {
  margin-bottom: 16px;
  padding: 16px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
}

.period-item:last-child {
  margin-bottom: 0;
}

.period-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.period-item-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  align-items: end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .restriction-section {
    padding: 16px;
  }

  .period-item-content {
    grid-template-columns: 1fr;
  }

  .hall-checkboxes {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }

  .cinema-hall-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  :deep(.el-radio-group) {
    flex-direction: column;
    gap: 8px;
  }
}

/* 动画效果 */
.restriction-section-animation {
  animation: fade-in-up 0.3s ease-out;
}

.period-item-animation {
  animation: slide-in 0.3s ease-out;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
