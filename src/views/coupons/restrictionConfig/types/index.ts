/**
 * 优惠券限制条件类型定义
 * 基于API文档：获取优惠券限制规则详情 和 创建优惠券限制规则
 */

// ==================== 基础类型 ====================

/** 影院信息 */
export interface Cinema {
  id?: number
  cinemaId: string
  cinemaName: string
  halls?: Hall[]
  selectedHalls?: string[]  // 选中的影厅ID列表
}

/** 影厅信息 */
export interface Hall {
  hallId: string
  hallName: string
}

/** 影片信息 */
export interface Film {
  id?: number
  filmId: number
  filmCode?: string
  filmName: string
}

/** 时间段信息 */
export interface Period {
  id?: number
  num: number        // 时段序号
  day: number        // 星期几
  start: number      // 开始时间：2030（晚上8点半）
  end: number        // 结束时间：2400（晚上12点）
}

/** 影片版本信息 */
export interface FilmVersion {
  filmVersion: string
  versionId: string
}

/** 演出信息 */
export interface Show {
  id?: string
  cinemaId: number
  cinemaName: string
  showName: string
  showId: string
  sessions?: ShowSession[]
}

/** 演出场次信息 */
export interface ShowSession {
  id?: string
  showScheduleId: number
  startTime: number
  endTime: number
}

/** 卖品信息 */
export interface Goods {
  id: number
  cinemaId: number
  cinemaName: string
  goodsId: string
  goodsName: string
}

/** 展览信息 */
export interface Exhibition {
  exhibitId: number
  exhibitName: string
}

/** 展览场馆信息 */
export interface ExhibitHouse {
  houseId?: string
  halls?: Hall[]
}

// ==================== 限制规则类型 ====================

/** 影院限制 */
export interface CinemaRestriction {
  cinemaScope: number  // 影院范围：全部0，指定1，排除2
  cinemas: Cinema[]
}

/** 影片限制 */
export interface FilmRestriction {
  filmScope: number    // 适用影片：全部影片0，指定影片1，最低价影片2
  films: Film[]
}

/** 时间段限制 */
export interface PeriodRestriction {
  periodScope: number  // 适用时段：全部时段0，指定时段1
  periods: Period[]
}

/** 影片版本限制 */
export interface FilmVersionRestriction {
  versionScope: number // 适用影片版本：全部影片版本0，指定影片版本1
  versions: FilmVersion[]
}

/** 影票优惠券限制规则 */
export interface TicketRestriction {
  couponId?: string
  id?: string
  cinemaRestriction: CinemaRestriction
  filmRestriction: FilmRestriction
  periodRestriction: PeriodRestriction
  filmVersionRestriction: FilmVersionRestriction
}

/** 演出优惠券限制规则 */
export interface ShowRestriction {
  showScope: number    // 适用演出: 全部0，指定1，排除2
  shows: Show[]
}

/** 卖品优惠券限制规则 */
export interface GoodsRestriction {
  goodsScope: number   // 适用卖品分类: 全部0，指定1，排除2
  cinemas: Cinema[]
  goodsTypeIds: number[]
  goods: Goods[]
}

/** 展览优惠券限制规则 */
export interface ExhibitionRestriction {
  exhibitScope: number // 展览范围：全部0，指定1，排除2
  exhibitHouses: ExhibitHouse[]
  exhibits: Exhibition[]
}

// ==================== 主要接口类型 ====================

/** 创建优惠券限制规则参数 */
export interface CreateRestrictionParams {
  couponId: string
  ticketRestriction?: TicketRestriction
  showRestriction?: ShowRestriction
  goodsRestriction?: GoodsRestriction
  exhibitionRestriction?: ExhibitionRestriction
}

/** 获取优惠券限制规则参数 */
export interface GetRestrictionParams {
  couponId: string
  useOn: number  // 适用商品：影票0，卖品1，演出2，展览3
}

/** 优惠券限制规则响应 */
export interface RestrictionResponse {
  couponId: string
  ticketRestriction?: TicketRestriction
  showRestriction?: ShowRestriction
  goodsRestriction?: GoodsRestriction
  exhibitionRestriction?: ExhibitionRestriction
}

// ==================== 枚举类型 ====================

/** 范围类型枚举 */
export enum ScopeType {
  ALL = 0,       // 全部
  SPECIFIED = 1, // 指定
  EXCLUDED = 2,  // 排除
}

/** 影片范围类型枚举 */
export enum FilmScopeType {
  ALL = 0,           // 全部影片
  SPECIFIED = 1,     // 指定影片
  LOWEST_PRICE = 2,  // 最低价影片
}

/** 时段范围类型枚举 */
export enum PeriodScopeType {
  ALL = 0,       // 全部时段
  SPECIFIED = 1, // 指定时段
}

/** 版本范围类型枚举 */
export enum VersionScopeType {
  ALL = 0,       // 全部影片版本
  SPECIFIED = 1, // 指定影片版本
}

/** 星期枚举 */
export enum WeekDay {
  SUNDAY = 0,    // 周日
  MONDAY = 1,    // 周一
  TUESDAY = 2,   // 周二
  WEDNESDAY = 3, // 周三
  THURSDAY = 4,  // 周四
  FRIDAY = 5,    // 周五
  SATURDAY = 6,  // 周六
}
