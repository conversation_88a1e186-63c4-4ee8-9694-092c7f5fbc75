<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { ElMessage, ElButton, ElText } from 'element-plus'

// 组件导入
import TicketRestriction from './components/TicketRestriction.vue'
import ShowRestriction from './components/ShowRestriction.vue'
import GoodsRestriction from './components/GoodsRestriction.vue'
import ExhibitionRestriction from './components/ExhibitionRestriction.vue'

// 类型导入
import type {
  CreateRestrictionParams,
  RestrictionResponse,
  TicketRestriction as TicketRestrictionType,
  ShowRestriction as ShowRestrictionType,
  GoodsRestriction as GoodsRestrictionType,
  ExhibitionRestriction as ExhibitionRestrictionType
} from './types'

// 常量导入
import {
  DEFAULT_TICKET_RESTRICTION,
  DEFAULT_SHOW_RESTRICTION,
  DEFAULT_GOODS_RESTRICTION,
  DEFAULT_EXHIBITION_RESTRICTION
} from './constants'

// API导入
import { getRestriction, createRestriction } from './api'

defineOptions({
  name: 'CouponRestrictionConfig',
})

// ==================== Props & Emits ====================

interface Props {
  couponId: string
  useOn: number  // 适用商品：影票0，卖品1，演出2，展览3
  pageMode?: 'add' | 'edit' | 'view'
}

interface Emits {
  (e: 'submit', data: CreateRestrictionParams): void
  (e: 'success', data: any): void
  (e: 'cancel'): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  pageMode: 'add',
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

// 加载状态
const loading = ref(false)

// 表单数据
const formData = ref<CreateRestrictionParams>({
  couponId: props.couponId,
})

// 各类型限制数据
const ticketRestriction = ref<TicketRestrictionType>({ ...DEFAULT_TICKET_RESTRICTION })
const showRestriction = ref<ShowRestrictionType>({ ...DEFAULT_SHOW_RESTRICTION })
const goodsRestriction = ref<GoodsRestrictionType>({ ...DEFAULT_GOODS_RESTRICTION })
const exhibitionRestriction = ref<ExhibitionRestrictionType>({ ...DEFAULT_EXHIBITION_RESTRICTION })

// ==================== 计算属性 ====================

// 当前限制类型标题
const restrictionTitle = computed(() => {
  switch (props.useOn) {
    case 0: return '影票限制条件'
    case 1: return '卖品限制条件'
    case 2: return '演出限制条件'
    case 3: return '展览限制条件'
    default: return '限制条件'
  }
})

// 当前限制类型描述
const restrictionDescription = computed(() => {
  switch (props.useOn) {
    case 0: return '设置影票优惠券的使用限制条件，包括影院、影片、时段、版本等限制'
    case 1: return '设置卖品优惠券的使用限制条件，包括影院、卖品分类、具体卖品等限制'
    case 2: return '设置演出优惠券的使用限制条件，包括演出范围、具体演出等限制'
    case 3: return '设置展览优惠券的使用限制条件，包括展览场馆、具体展览等限制'
    default: return '设置优惠券的使用限制条件'
  }
})

// 是否只读模式
const isReadonly = computed(() => props.pageMode === 'view')

// ==================== 业务逻辑 ====================

/**
 * 加载限制规则数据
 */
async function loadRestrictionData() {
  if (!props.couponId) return

  try {
    loading.value = true
    const response = await getRestriction({
      couponId: props.couponId,
      useOn: props.useOn
    })

    const data = response.data

    // 根据类型设置对应的限制数据
    switch (props.useOn) {
      case 0: // 影票
        if (data.ticketRestriction) {
          ticketRestriction.value = { ...data.ticketRestriction }
        }
        break
      case 1: // 卖品
        if (data.goodsRestriction) {
          goodsRestriction.value = { ...data.goodsRestriction }
        }
        break
      case 2: // 演出
        if (data.showRestriction) {
          showRestriction.value = { ...data.showRestriction }
        }
        break
      case 3: // 展览
        if (data.exhibitionRestriction) {
          exhibitionRestriction.value = { ...data.exhibitionRestriction }
        }
        break
    }
  } catch (error) {
    console.error('加载限制规则失败:', error)
    ElMessage.error('加载限制规则失败')
  } finally {
    loading.value = false
  }
}

/**
 * 构建提交数据
 */
function buildSubmitData(): CreateRestrictionParams {
  const submitData: CreateRestrictionParams = {
    couponId: props.couponId,
  }

  // 根据类型添加对应的限制数据
  switch (props.useOn) {
    case 0: // 影票
      submitData.ticketRestriction = {
        ...ticketRestriction.value,
        couponId: props.couponId,
      }
      break
    case 1: // 卖品
      submitData.goodsRestriction = { ...goodsRestriction.value }
      break
    case 2: // 演出
      submitData.showRestriction = { ...showRestriction.value }
      break
    case 3: // 展览
      submitData.exhibitionRestriction = { ...exhibitionRestriction.value }
      break
  }

  return submitData
}

/**
 * 验证表单数据
 */
function validateForm(): boolean {
  // 基础验证
  if (!props.couponId) {
    ElMessage.error('优惠券ID不能为空')
    return false
  }

  // 根据类型进行特定验证
  switch (props.useOn) {
    case 0: // 影票
      // 验证影院限制
      if (ticketRestriction.value.cinemaRestriction.cinemaScope !== 0 &&
          ticketRestriction.value.cinemaRestriction.cinemas.length === 0) {
        ElMessage.error('请选择适用的影院')
        return false
      }

      // 验证影片限制
      if (ticketRestriction.value.filmRestriction.filmScope === 1 &&
          ticketRestriction.value.filmRestriction.films.length === 0) {
        ElMessage.error('请选择适用的影片')
        return false
      }

      // 验证时段限制
      if (ticketRestriction.value.periodRestriction.periodScope === 1 &&
          ticketRestriction.value.periodRestriction.periods.length === 0) {
        ElMessage.error('请添加时段限制')
        return false
      }

      // 验证版本限制
      if (ticketRestriction.value.filmVersionRestriction.versionScope === 1 &&
          ticketRestriction.value.filmVersionRestriction.versions.length === 0) {
        ElMessage.error('请选择适用的影片版本')
        return false
      }
      break

    case 1: // 卖品
      if (goodsRestriction.value.goodsScope !== 0 &&
          goodsRestriction.value.cinemas.length === 0 &&
          goodsRestriction.value.goodsTypeIds.length === 0 &&
          goodsRestriction.value.goods.length === 0) {
        ElMessage.error('请至少选择一种卖品限制条件')
        return false
      }
      break

    case 2: // 演出
      if (showRestriction.value.showScope !== 0 &&
          showRestriction.value.shows.length === 0) {
        ElMessage.error('请选择适用的演出')
        return false
      }
      break

    case 3: // 展览
      if (exhibitionRestriction.value.exhibitScope !== 0 &&
          exhibitionRestriction.value.exhibitHouses.length === 0 &&
          exhibitionRestriction.value.exhibits.length === 0) {
        ElMessage.error('请至少选择展览场馆或具体展览')
        return false
      }
      break
  }

  return true
}

/**
 * 提交表单
 */
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  try {
    loading.value = true
    const submitData = buildSubmitData()

    console.log('提交限制规则数据:', submitData)

    if (props.pageMode === 'add') {
      const response = await createRestriction(submitData)
      if (response.code === 0) {
        ElMessage.success('创建限制规则成功')
        emit('success', response.data)
      } else {
        ElMessage.error(response.msg || '创建限制规则失败')
      }
    } else {
      // 编辑模式
      emit('submit', submitData)
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

/**
 * 重置表单
 */
function handleReset() {
  switch (props.useOn) {
    case 0:
      ticketRestriction.value = { ...DEFAULT_TICKET_RESTRICTION }
      break
    case 1:
      goodsRestriction.value = { ...DEFAULT_GOODS_RESTRICTION }
      break
    case 2:
      showRestriction.value = { ...DEFAULT_SHOW_RESTRICTION }
      break
    case 3:
      exhibitionRestriction.value = { ...DEFAULT_EXHIBITION_RESTRICTION }
      break
  }
  ElMessage.info('表单已重置')
}

/**
 * 取消操作
 */
function handleCancel() {
  emit('cancel')
}

// ==================== 监听器 ====================

// 监听couponId变化，重新加载数据
watch(() => props.couponId, (newId) => {
  if (newId && props.pageMode === 'edit') {
    loadRestrictionData()
  }
}, { immediate: true })

// ==================== 暴露方法 ====================

defineExpose({
  handleSubmit,
  handleReset,
  validateForm,
  buildSubmitData,
})
</script>

<template>
  <div class="coupon-restriction-config">
    <div class="config-header">
      <div class="header-content">
        <h2>{{ restrictionTitle }}</h2>
        <p>{{ restrictionDescription }}</p>
      </div>
    </div>

    <div class="config-content" v-loading="loading">
      <!-- 根据useOn类型显示不同的限制配置组件 -->
      <TicketRestriction
        v-if="useOn === 0"
        v-model="ticketRestriction"
        :readonly="isReadonly"
      />

      <GoodsRestriction
        v-else-if="useOn === 1"
        v-model="goodsRestriction"
        :readonly="isReadonly"
      />

      <ShowRestriction
        v-else-if="useOn === 2"
        v-model="showRestriction"
        :readonly="isReadonly"
      />

      <ExhibitionRestriction
        v-else-if="useOn === 3"
        v-model="exhibitionRestriction"
        :readonly="isReadonly"
      />

      <!-- 未知类型提示 -->
      <div v-else class="unknown-type">
        <ElText type="warning">
          未知的优惠券类型：{{ useOn }}
        </ElText>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div v-if="!isReadonly" class="config-actions">
      <ElButton @click="handleReset">
        重置
      </ElButton>
      <ElButton @click="handleCancel">
        取消
      </ElButton>
      <ElButton type="primary" :loading="loading" @click="handleSubmit">
        {{ pageMode === 'add' ? '创建限制规则' : '保存修改' }}
      </ElButton>
    </div>
  </div>
</template>

<style scoped>
.coupon-restriction-config {
  padding: 20px;
  background: var(--el-bg-color);
  border-radius: 8px;
}

.config-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid var(--el-color-primary);
}

.header-content h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.header-content p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.config-content {
  min-height: 400px;
}

.unknown-type {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background: var(--el-fill-color-light);
  border-radius: 8px;
}

.config-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding: 24px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.config-actions .el-button {
  min-width: 100px;
  border-radius: 6px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .coupon-restriction-config {
    padding: 12px;
  }

  .config-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .config-actions .el-button {
    min-width: auto;
  }
}

/* 动画效果 */
.config-content-animation {
  animation: fade-in 0.3s ease-out;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
