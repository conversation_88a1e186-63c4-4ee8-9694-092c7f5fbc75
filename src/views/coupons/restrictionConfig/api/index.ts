/**
 * 优惠券限制条件API接口
 */

import type { 
  CreateRestrictionParams, 
  GetRestrictionParams, 
  RestrictionResponse,
  Cinema,
  Film,
  Show,
  Goods,
  Exhibition,
  FilmVersion
} from '../types'

// ==================== 模拟数据 ====================

/** 模拟影院数据 */
const mockCinemas: Cinema[] = [
  {
    id: 1,
    cinemaId: 'cinema_001',
    cinemaName: '万达影城（CBD店）',
    selectedHalls: [],
    halls: [
      { hallId: 'hall_001', hallName: '1号厅' },
      { hallId: 'hall_002', hallName: '2号厅' },
      { hallId: 'hall_003', hallName: 'IMAX厅' },
      { hallId: 'hall_004', hallName: '3号厅' },
      { hallId: 'hall_005', hallName: '4号厅' },
    ]
  },
  {
    id: 2,
    cinemaId: 'cinema_002',
    cinemaName: '星美国际影城',
    selectedHalls: [],
    halls: [
      { hallId: 'hall_006', hallName: '1号厅' },
      { hallId: 'hall_007', hallName: '2号厅' },
      { hallId: 'hall_008', hallName: '3号厅' },
      { hallId: 'hall_009', hallName: 'VIP厅' },
    ]
  },
  {
    id: 3,
    cinemaId: 'cinema_003',
    cinemaName: '金逸影城（购物中心店）',
    selectedHalls: [],
    halls: [
      { hallId: 'hall_010', hallName: 'VIP厅' },
      { hallId: 'hall_011', hallName: '杜比全景声厅' },
      { hallId: 'hall_012', hallName: 'IMAX厅' },
    ]
  },
  {
    id: 4,
    cinemaId: 'cinema_004',
    cinemaName: '大地影院（市中心店）',
    selectedHalls: [],
    halls: [
      { hallId: 'hall_013', hallName: '1号厅' },
      { hallId: 'hall_014', hallName: '2号厅' },
      { hallId: 'hall_015', hallName: '3号厅' },
      { hallId: 'hall_016', hallName: '4号厅' },
      { hallId: 'hall_017', hallName: '5号厅' },
      { hallId: 'hall_018', hallName: 'IMAX厅' },
    ]
  },
  {
    id: 5,
    cinemaId: 'cinema_005',
    cinemaName: '华谊兄弟影院',
    selectedHalls: [],
    halls: [
      { hallId: 'hall_019', hallName: '激光厅' },
      { hallId: 'hall_020', hallName: '杜比厅' },
    ]
  }
]

/** 模拟影片数据 */
const mockFilms: Film[] = [
  { id: 1, filmId: 1001, filmCode: 'CN2024001', filmName: '流浪地球3' },
  { id: 2, filmId: 1002, filmCode: 'US2024001', filmName: '复仇者联盟5' },
  { id: 3, filmId: 1003, filmCode: 'CN2024002', filmName: '唐人街探案4' },
  { id: 4, filmId: 1004, filmCode: 'JP2024001', filmName: '你的名字2' },
  { id: 5, filmId: 1005, filmCode: 'US2024002', filmName: '阿凡达3' },
]

/** 模拟影片版本数据 */
const mockFilmVersions: FilmVersion[] = [
  { filmVersion: '2D', versionId: 'version_2d' },
  { filmVersion: '3D', versionId: 'version_3d' },
  { filmVersion: 'IMAX 2D', versionId: 'version_imax_2d' },
  { filmVersion: 'IMAX 3D', versionId: 'version_imax_3d' },
  { filmVersion: '杜比全景声', versionId: 'version_dolby' },
  { filmVersion: '4DX', versionId: 'version_4dx' },
]

/** 模拟演出数据 */
const mockShows: Show[] = [
  {
    id: 'show_001',
    cinemaId: 1,
    cinemaName: '国家大剧院',
    showName: '天鹅湖',
    showId: 'show_swan_lake',
    sessions: [
      { id: 'session_001', showScheduleId: 1001, startTime: 1704067200000, endTime: 1704074400000 },
      { id: 'session_002', showScheduleId: 1002, startTime: 1704153600000, endTime: 1704160800000 },
    ]
  },
  {
    id: 'show_002',
    cinemaId: 2,
    cinemaName: '上海大剧院',
    showName: '歌剧魅影',
    showId: 'show_phantom',
    sessions: [
      { id: 'session_003', showScheduleId: 1003, startTime: 1704240000000, endTime: 1704247200000 },
    ]
  }
]

/** 模拟卖品数据 */
const mockGoods: Goods[] = [
  { id: 1, cinemaId: 1, cinemaName: '万达影城（CBD店）', goodsId: 'goods_001', goodsName: '爆米花（大）' },
  { id: 2, cinemaId: 1, cinemaName: '万达影城（CBD店）', goodsId: 'goods_002', goodsName: '可乐（中杯）' },
  { id: 3, cinemaId: 2, cinemaName: '星美国际影城', goodsId: 'goods_003', goodsName: '薯条' },
  { id: 4, cinemaId: 2, cinemaName: '星美国际影城', goodsId: 'goods_004', goodsName: '热狗' },
]

/** 模拟展览数据 */
const mockExhibitions: Exhibition[] = [
  { exhibitId: 1, exhibitName: '梵高艺术展' },
  { exhibitId: 2, exhibitName: '古埃及文明展' },
  { exhibitId: 3, exhibitName: '现代艺术精品展' },
]

// ==================== API接口 ====================

/**
 * 获取优惠券限制规则详情
 */
export async function getRestriction(params: GetRestrictionParams): Promise<{ data: RestrictionResponse; code: number; msg: string }> {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500))
  
  const { couponId, useOn } = params
  
  // 根据useOn返回不同的限制规则
  const response: RestrictionResponse = {
    couponId,
  }
  
  switch (useOn) {
    case 0: // 影票
      response.ticketRestriction = {
        couponId,
        id: `restriction_${couponId}`,
        cinemaRestriction: {
          cinemaScope: 0,
          cinemas: []
        },
        filmRestriction: {
          filmScope: 0,
          films: []
        },
        periodRestriction: {
          periodScope: 0,
          periods: []
        },
        filmVersionRestriction: {
          versionScope: 0,
          versions: []
        }
      }
      break
    case 1: // 卖品
      response.goodsRestriction = {
        goodsScope: 0,
        cinemas: [],
        goodsTypeIds: [],
        goods: []
      }
      break
    case 2: // 演出
      response.showRestriction = {
        showScope: 0,
        shows: []
      }
      break
    case 3: // 展览
      response.exhibitionRestriction = {
        exhibitScope: 0,
        exhibitHouses: [],
        exhibits: []
      }
      break
  }
  
  return {
    data: response,
    code: 0,
    msg: 'success'
  }
}

/**
 * 创建优惠券限制规则
 */
export async function createRestriction(params: CreateRestrictionParams): Promise<{ data: any; code: number; msg: string }> {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 800))
  
  console.log('创建限制规则参数:', params)
  
  return {
    data: { id: `restriction_${Date.now()}` },
    code: 0,
    msg: '创建成功'
  }
}

/**
 * 获取影院列表
 */
export async function getCinemaList(): Promise<{ data: Cinema[]; code: number; msg: string }> {
  await new Promise(resolve => setTimeout(resolve, 300))
  
  return {
    data: mockCinemas,
    code: 0,
    msg: 'success'
  }
}

/**
 * 获取影片列表
 */
export async function getFilmList(): Promise<{ data: Film[]; code: number; msg: string }> {
  await new Promise(resolve => setTimeout(resolve, 300))
  
  return {
    data: mockFilms,
    code: 0,
    msg: 'success'
  }
}

/**
 * 获取影片版本列表
 */
export async function getFilmVersionList(): Promise<{ data: FilmVersion[]; code: number; msg: string }> {
  await new Promise(resolve => setTimeout(resolve, 200))
  
  return {
    data: mockFilmVersions,
    code: 0,
    msg: 'success'
  }
}

/**
 * 获取演出列表
 */
export async function getShowList(): Promise<{ data: Show[]; code: number; msg: string }> {
  await new Promise(resolve => setTimeout(resolve, 300))
  
  return {
    data: mockShows,
    code: 0,
    msg: 'success'
  }
}

/**
 * 获取卖品列表
 */
export async function getGoodsList(): Promise<{ data: Goods[]; code: number; msg: string }> {
  await new Promise(resolve => setTimeout(resolve, 300))
  
  return {
    data: mockGoods,
    code: 0,
    msg: 'success'
  }
}

/**
 * 获取展览列表
 */
export async function getExhibitionList(): Promise<{ data: Exhibition[]; code: number; msg: string }> {
  await new Promise(resolve => setTimeout(resolve, 200))
  
  return {
    data: mockExhibitions,
    code: 0,
    msg: 'success'
  }
}
