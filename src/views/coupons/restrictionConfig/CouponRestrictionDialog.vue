<script setup lang="ts">
// 类型导入
import type { CreateRestrictionParams, RestrictionResponse } from './types'
import { ElDialog } from 'element-plus'

import { computed, ref, watch } from 'vue'

// 导入主配置组件
import CouponRestrictionConfig from './CouponRestrictionConfig.vue'

defineOptions({
  name: 'CouponRestrictionDialog',
})

const props = withDefaults(defineProps<Props>(), {
  pageMode: 'add',
  title: '',
  width: '1200px',
  destroyOnClose: true,
  closeOnClickModal: false,
})

const emit = defineEmits<Emits>()

// ==================== Props & Emits ====================

interface Props {
  modelValue: boolean
  couponId: string
  useOn: number // 适用商品：影票0，卖品1，演出2，展览3
  pageMode?: 'add' | 'edit' | 'view'
  title?: string
  width?: string | number
  destroyOnClose?: boolean
  closeOnClickModal?: boolean
  restrictionData?: RestrictionResponse
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', data: CreateRestrictionParams): void
  (e: 'success', data: any): void
  (e: 'cancel'): void
  (e: 'close'): void
}

// ==================== 响应式数据 ====================

// 配置组件引用
const configRef = ref()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 动态标题
const dialogTitle = computed(() => {
  if (props.title) { return props.title }

  const typeMap = {
    0: '影票',
    1: '卖品',
    2: '演出',
    3: '展览',
  }

  const typeName = typeMap[props.useOn as keyof typeof typeMap] || '优惠券'

  switch (props.pageMode) {
    case 'add': return `创建${typeName}限制条件`
    case 'edit': return `编辑${typeName}限制条件`
    case 'view': return `查看${typeName}限制条件`
    default: return `${typeName}限制条件配置`
  }
})

// 是否只读模式
const isReadonly = computed(() => props.pageMode === 'view')

// ==================== 事件处理 ====================

/**
 * 处理表单提交
 */
function handleSubmit(data: CreateRestrictionParams) {
  emit('submit', data)
}

/**
 * 处理成功操作
 */
function handleSuccess(data: any) {
  emit('success', data)
}

/**
 * 处理取消操作
 */
function handleCancel() {
  emit('cancel')
  dialogVisible.value = false
}

/**
 * 处理对话框关闭
 */
function handleClose() {
  emit('close')
}

/**
 * 处理对话框打开前的操作
 */
function handleOpen() {
  // 对话框打开时的逻辑
}

/**
 * 处理对话框关闭后的操作
 */
function handleClosed() {
  // 对话框关闭后的清理逻辑
}

/**
 * 外部调用：提交表单
 */
function submit() {
  return configRef.value?.handleSubmit()
}

/**
 * 外部调用：重置表单
 */
function reset() {
  return configRef.value?.handleReset()
}

/**
 * 外部调用：验证表单
 */
function validate() {
  return configRef.value?.validateForm()
}

/**
 * 外部调用：获取表单数据
 */
function getFormData() {
  return configRef.value?.buildSubmitData()
}

// ==================== 监听器 ====================

// 监听对话框显示状态，在打开时加载数据
watch(() => props.modelValue, (visible) => {
  if (visible && props.pageMode === 'edit' && props.restrictionData) {
    // 编辑模式下预填充数据的逻辑可以在这里处理
    console.log('编辑模式，预填充数据:', props.restrictionData)
  }
})

// ==================== 暴露方法 ====================

defineExpose({
  submit,
  reset,
  validate,
  getFormData,
})
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="width"
    :destroy-on-close="destroyOnClose"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="!isReadonly"
    :show-close="true"
    draggable
    align-center
    class="coupon-restriction-dialog"
    @open="handleOpen"
    @close="handleClose"
    @closed="handleClosed"
  >
    <!-- 对话框内容 -->
    <div class="dialog-content">
      <CouponRestrictionConfig
        ref="configRef"
        :coupon-id="couponId"
        :use-on="useOn"
        :page-mode="pageMode"
        @submit="handleSubmit"
        @success="handleSuccess"
        @cancel="handleCancel"
        @close="handleCancel"
      />
    </div>

    <!-- 自定义底部按钮（如果需要的话） -->
    <template v-if="false" #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">
          取消
        </ElButton>
        <ElButton v-if="!isReadonly" type="primary" @click="submit">
          {{ pageMode === 'add' ? '创建限制规则' : '保存修改' }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.coupon-restriction-dialog {
  --el-dialog-border-radius: 12px;
}

/* 对话框内容样式 */
.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0;
}

/* 自定义滚动条 */
.dialog-content::-webkit-scrollbar {
  width: 6px;
}

.dialog-content::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark);
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb:hover {
  background: var(--el-fill-color-darker);
}

/* 对话框底部按钮样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0 0 0;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 对话框头部样式优化 */
:deep(.el-dialog__header) {
  padding: 20px 24px 16px 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color-page);
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

:deep(.el-dialog__headerbtn) {
  top: 20px;
  right: 24px;
  width: 32px;
  height: 32px;
}

:deep(.el-dialog__close) {
  font-size: 16px;
  color: var(--el-text-color-regular);
}

:deep(.el-dialog__close:hover) {
  color: var(--el-color-primary);
}

/* 对话框主体样式 */
:deep(.el-dialog__body) {
  padding: 0;
  background: var(--el-bg-color);
}

/* 隐藏内部组件的操作按钮区域 */
:deep(.config-actions) {
  display: none;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  :deep(.el-dialog) {
    width: 90% !important;
    max-width: 1000px;
  }
}

@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }

  .dialog-content {
    max-height: 80vh;
  }

  :deep(.el-dialog__header) {
    padding: 16px 20px 12px 20px;
  }

  :deep(.el-dialog__headerbtn) {
    top: 16px;
    right: 20px;
  }
}

/* 动画效果 */
:deep(.el-dialog) {
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 遮罩层样式 */
:deep(.el-overlay) {
  backdrop-filter: blur(2px);
}

/* 拖拽时的样式 */
:deep(.el-dialog.is-dragging) {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}
</style>
