<script setup lang="ts">
import type { CreateCouponParams, UpdateCouponParams } from '@/api/modules/coupons'
// import type { CreateCouponParams } from '@/views/coupons/baseConfig/types'
import { Delete, InfoFilled, Lock, Plus } from '@element-plus/icons-vue'

import { ElMessage } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'

// API导入
import { getAllChannel } from '@/api/modules/channel/index.js'
import { createCoupon, updateCoupon } from '@/api/modules/coupons'

// 常量导入
import {
  CHANNEL_SCOPE_OPTIONS,
  COUPON_TYPE_OPTIONS,
  MATCH_RULE_OPTIONS,
  SERVICE_FEE_REDUCTION_OPTIONS,
  USE_ON_TYPE_OPTIONS,
} from '@/views/coupons/baseConfig/constants'
import CouponSettleMethod from '@/views/coupons/baseConfig/CouponSettleMethod.vue'
// 类型导入

import CouponValidityPeriod from '@/views/coupons/baseConfig/CouponValidityPeriod.vue'

import { CouponType, UseOnType } from '@/views/coupons/baseConfig/types'
import { ServiceFeeReduction } from '@/views/coupons/baseConfig/types/index.ts'
// 子组件导入
import CouponGenerateType from './CouponGenerateType.vue'

defineOptions({
  name: 'CouponBaseConfig',
})

const props = withDefaults(defineProps<Props>(), {
  pageMode: 'add',
})

const emit = defineEmits<Emits>()

// ==================== Props & Emits ====================

interface Props {
  pageMode?: 'add' | 'edit' | 'view'
  couponData?: CreateCouponParams
}

interface Emits {
  (e: 'submit', data: CreateCouponParams): void
  (e: 'cancel'): void
  (e: 'next-step', couponId: string): void
  (e: 'refresh'): void
  (e: 'close'): void
}

// ==================== 响应式数据 ====================

// 表单引用
const formRef = ref()

// 渠道选项
const channelOptions = ref<Array<{ label: string, value: string }>>([])

// 加载状态
const loading = ref(false)

// 主表单数据 - 完全按照API接口结构
const formData = ref<CreateCouponParams>({
  name: '', // 优惠券名称
  useOn: 0, // 适用商品：影票0，卖品1，演出2，展览3，电商4
  reduction: 1, // 服务费减免：减免0，不减免1
  userServiceFee: 300, // 用户服务费(分)
  note: '', // 备注信息
  remark: '', // 备注说明
  couponType: 0, // 优惠券类型：满减0，减至1，通兑2，折扣3，多对一4
  priceRule: { // 价格规则
    priceTiers: [ // 价格层级数组
      {
        priceOrigin: 0, // 原始价格(分)
        priceReduce: 0, // 减免金额(分)
        priceDiff: 0, // 价格差值(分)
      },
    ],
    matchRule: 0, // 匹配规则 0 优惠补差 1 最低票价
  },
  channelRule: { // 渠道规则
    channelScope: 2, // 渠道范围 1-指定渠道 2-全部渠道
    channels: [], // 适用渠道列表
  },
  generateRule: { // 生成规则
    generateType: 1, // 生成类型：0手动，1自动
    num: 100, // 生成数量
    batchSize: 100, // 批次大小
  },
  settleRule: { // 结算规则
    method: 0, // 结算方式 0:定价规则 1:影院团购价 2.自定义结算
    priced: { // 自定义结算配置
      type: 0, // 计算方式 0-固定价格 1-最低票价
      money: 0, // 定价金额(单位：分)
    },
  },
  periodRule: { // 时间有效规则
    validScope: 0, // 有效期类型：0固定时间，1相对时间
    startTime: 0, // 开始时间（固定时间模式）
    endTime: 0, // 结束时间（固定时间模式）
    overdueDay: 30, // 有效天数（相对时间模式）
  },
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入优惠券名称', trigger: 'blur' },
    { min: 1, max: 50, message: '优惠券名称长度在 1 到 50 个字符', trigger: 'blur' },
  ],
  useOn: [
    { required: true, message: '请选择适用商品类型', trigger: 'change' },
  ],
  couponType: [
    { required: true, message: '请选择优惠券类型', trigger: 'change' },
  ],
  reduction: [
    { required: true, message: '请选择服务费减免方式', trigger: 'change' },
  ],
  userServiceFee: [
    { required: true, message: '请输入用户服务费', trigger: 'blur' },
    { type: 'number', min: 0, max: 999999, message: '用户服务费范围为 0-9999.99 元', trigger: 'blur' },
  ],
}

// ==================== 计算属性 ====================

// 用户服务费（元）
const userServiceFeeYuan = computed({
  get: () => formData.value.userServiceFee / 100,
  set: (value: number) => {
    formData.value.userServiceFee = Math.round(value * 100)
  },
})

// 价格规则标签
const priceRuleLabel = computed(() => {
  const { couponType } = formData.value
  switch (couponType) {
    case 0: return '满减规则'
    case 1: return '减至规则'
    case 2: return '通兑规则'
    case 3: return '折扣规则'
    case 4: return '多对一规则'
    default: return '价格规则'
  }
})

// 是否可以添加多条价格规则
const canAddMultiplePriceTiers = computed(() => {
  return formData.value.priceRule?.matchRule === 0 // 优惠补差时可以添加多条
})

// 最大价格规则数量
const maxPriceTiers = computed(() => {
  return formData.value.priceRule?.matchRule === 0 ? 10 : 1
})

// ==================== 业务逻辑函数 ====================

/**
 * 加载渠道选项
 */
async function loadChannelOptions() {
  try {
    loading.value = true
    const response = await getAllChannel()
    const { data } = response

    channelOptions.value = data.map((item: any) => ({
      label: item.channelName,
      value: item.id,
    }))
  }
  catch (error) {
    console.error('加载渠道选项失败:', error)
    ElMessage.error('加载渠道选项失败')
  }
  finally {
    loading.value = false
  }
}

/**
 * 根据优惠券类型设置价格规则默认值
 */
function setPriceRuleDefaults() {
  const { couponType } = formData.value

  // 重置价格规则
  formData.value.priceRule = {
    priceTiers: [
      {
        priceOrigin: 0,
        priceReduce: 0,
        priceDiff: 0,
      },
    ],
    matchRule: 0, // 默认为优惠补差
  }

  // 根据优惠券类型设置特定的默认值
  switch (couponType) {
    case 0: // 满减券
      formData.value.priceRule.priceTiers[0] = {
        priceOrigin: 1000, // 满1000分
        priceReduce: 100, // 减100分
        priceDiff: 0,
      }
      break
    case 1: // 减至券
      formData.value.priceRule.priceTiers[0] = {
        priceOrigin: 0,
        priceReduce: 500, // 减至500分
        priceDiff: 0,
      }
      break
    case 2: // 通兑券
      // 电影票 + 通兑券 + 最低价：只设置减免金额
      if (formData.value.useOn === 0 && formData.value.priceRule.matchRule === 1) {
        formData.value.priceRule.priceTiers[0] = {
          priceOrigin: 0,
          priceReduce: 500, // 减免500分（5元）
          priceDiff: 0,
        }
      }
      else {
        // 其他情况：设置完整的通兑券配置
        formData.value.priceRule.priceTiers[0] = {
          priceOrigin: 1000, // 最小1000分
          priceReduce: 2000, // 最大2000分
          priceDiff: 100, // 补差100分
        }
      }
      break
    case 3: // 折扣券
      formData.value.priceRule.priceTiers[0] = {
        priceOrigin: 1000, // 满1000分
        priceReduce: 80, // 8折(80%)
        priceDiff: 0,
      }
      break
    case 4: // 多对一券
      formData.value.priceRule.priceTiers[0] = {
        priceOrigin: 0,
        priceReduce: 2, // 2张兑1张
        priceDiff: 0,
      }
      break
    default:
      break
  }
}

/**
 * 添加价格规则
 */
function addPriceTier() {
  if (!canAddMultiplePriceTiers.value) {
    ElMessage.warning('当前匹配规则只能设置一条价格规则')
    return
  }

  if (formData.value.priceRule!.priceTiers.length >= maxPriceTiers.value) {
    ElMessage.warning(`最多只能添加${maxPriceTiers.value}条价格规则`)
    return
  }

  formData.value.priceRule!.priceTiers.push({
    priceOrigin: 0,
    priceReduce: 0,
    priceDiff: 0,
  })
}

/**
 * 删除价格规则
 */
function removePriceTier(index: number) {
  if (formData.value.priceRule!.priceTiers.length <= 1) {
    ElMessage.warning('至少需要保留一条价格规则')
    return
  }

  formData.value.priceRule!.priceTiers.splice(index, 1)
}

/**
 * 处理匹配规则变化
 */
function handleMatchRuleChange() {
  const { matchRule } = formData.value.priceRule!

  if (matchRule === 1) { // 最低票价
    // 只保留第一条规则
    formData.value.priceRule!.priceTiers = [formData.value.priceRule!.priceTiers[0]]
  }
}

/**
 * 处理渠道范围变化
 */
function handleChannelScopeChange() {
  const { channelScope } = formData.value.channelRule!

  if (channelScope === 2) { // 全部渠道
    formData.value.channelRule!.channels = []
  }
}

/**
 * 验证表单数据
 */
async function validateForm(): Promise<boolean> {
  try {
    await formRef.value?.validate()

    // 验证价格规则
    const { priceTiers } = formData.value.priceRule!
    for (let i = 0; i < priceTiers.length; i++) {
      const tier = priceTiers[i]
      if (tier.priceOrigin < 0 || tier.priceReduce < 0 || tier.priceDiff < 0) {
        ElMessage.error(`第${i + 1}条价格规则的金额不能为负数`)
        return false
      }
    }

    // 验证渠道规则
    const { channelScope, channels } = formData.value.channelRule!
    if (channelScope === 1 && channels.length === 0) {
      ElMessage.error('指定渠道时必须选择至少一个渠道')
      return false
    }

    return true
  }
  catch (error) {
    console.error('表单验证失败:', error)
    return false
  }
}

/**
 * 提交表单
 */
async function handleSubmit() {
  if (!await validateForm()) {
    return
  }

  try {
    loading.value = true

    // 构建提交数据，完全按照API接口格式
    const submitData: CreateCouponParams = {
      ...formData.value,
      // 确保数值类型正确
      userServiceFee: Number(formData.value.userServiceFee),
      useOn: Number(formData.value.useOn),
      couponType: Number(formData.value.couponType),
      reduction: Number(formData.value.reduction),
    }

    if (props.pageMode === 'add') {
      // 创建优惠券
      const response = await createCoupon(submitData as CreateCouponParams)
      const { data, code, msg } = response
      if (code === 0) {
        ElMessage.success('创建优惠券成功')
        emit('next-step', data.id || data)
      }
      else {
        ElMessage.error(msg || '创建优惠券失败')
      }
    }
    else {
      console.log('编辑模式提交数据:', submitData)
      const response = await updateCoupon(submitData as UpdateCouponParams)
      const { data, code, msg } = response
      if (code === 0) {
        ElMessage.success('更新优惠券成功')
        emit('success', data)
      }
      else {
        ElMessage.error(msg || '更新优惠券失败')
      }
      // 编辑模式
      emit('submit', submitData)
    }
  }
  catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  }
  finally {
    loading.value = false
  }
}

/**
 * 重置表单
 */
function handleReset() {
  formRef.value?.resetFields()
  setPriceRuleDefaults()
}

// ==================== 监听器 ====================

// 监听优惠券类型变化，设置默认价格规则
watch(() => formData.value.couponType, () => {
  setPriceRuleDefaults()
})

// 监听匹配规则变化
watch(() => formData.value.priceRule?.matchRule, () => {
  handleMatchRuleChange()
})

// 监听渠道范围变化
watch(() => formData.value.channelRule?.channelScope, () => {
  handleChannelScopeChange()
})

// 监听props变化，初始化表单数据
watch(() => props.couponData, (newData) => {
  if (newData) {
    Object.assign(formData.value, newData)
  }
}, { immediate: true, deep: true })

// ==================== 生命周期 ====================

onMounted(() => {
  loadChannelOptions()
  setPriceRuleDefaults()
})

// ==================== 暴露方法 ====================

defineExpose({
  formData,
  handleSubmit,
  handleReset,
  validateForm,
})
</script>

<template>
  <div class="coupon-base-config">
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="left"
      class="config-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">
          <h3>基本信息</h3>
          <p>设置优惠券的基本信息和适用范围</p>
        </div>

        <div class="form-grid">
          <!-- 优惠券名称 -->
          <ElFormItem label="优惠券名称" prop="name" class="form-item-full">
            <ElInput
              v-model="formData.name"
              placeholder="请输入优惠券名称"
              maxlength="50"
              show-word-limit
              clearable
            />
          </ElFormItem>

          <!-- 适用商品 -->
          <ElFormItem label="适用商品" prop="useOn">
            <ElSelect
              v-model="formData.useOn"
              :disabled="pageMode === 'edit' || pageMode === 'view'"
              placeholder="请选择适用商品类型"
              style="width: 100%"
            >
              <ElOption
                v-for="option in USE_ON_TYPE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
            <ElText v-if="pageMode === 'edit'" type="warning" size="small" class="mt-1">
              <ElIcon><InfoFilled /></ElIcon>
              适用商品类型创建后不可修改
            </ElText>
          </ElFormItem>

          <!-- 优惠券类型 -->
          <ElFormItem label="优惠券类型" prop="couponType">
            <ElSelect
              v-model="formData.couponType"
              :disabled="pageMode === 'edit' || pageMode === 'view'"
              placeholder="请选择优惠券类型"
              style="width: 100%"
            >
              <ElOption
                v-for="option in COUPON_TYPE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
            <ElText v-if="pageMode === 'edit'" type="warning" size="small" class="mt-1">
              <ElIcon><InfoFilled /></ElIcon>
              优惠券类型创建后不可修改
            </ElText>
          </ElFormItem>

          <!-- 服务费减免 -->
          <ElFormItem label="服务费减免" prop="reduction">
            <ElSelect
              v-model="formData.reduction"
              placeholder="请选择服务费减免方式"
              style="width: 100%"
              :disabled="pageMode === 'edit' || pageMode === 'view'"
            >
              <ElOption
                v-for="option in SERVICE_FEE_REDUCTION_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>

          <!-- 用户服务费 -->
          <ElFormItem v-if="formData.reduction === ServiceFeeReduction.NO_REDUCE" label="用户服务费" prop="userServiceFee">
            <ElInputNumber
              v-model="userServiceFeeYuan"
              :disabled="pageMode === 'edit' || pageMode === 'view'"
              :min="0"
              :max="9999.99"
              :precision="2"
              :step="1"
              style="width: 100%"
            >
              <template #suffix>
                元
              </template>
            </ElInputNumber>
          </ElFormItem>
        </div>
      </div>

      <!-- 价格规则 -->
      <div class="form-section" :class="{ 'form-section-disabled': pageMode === 'edit' || pageMode === 'view' }">
        <div class="section-title">
          <h3>{{ priceRuleLabel }}</h3>
          <p>根据优惠券类型设置相应的价格规则</p>
          <ElText v-if="pageMode === 'edit'" type="warning" size="small" class="edit-warning">
            <ElIcon><Lock /></ElIcon>
            价格规则创建后不可修改
          </ElText>
        </div>

        <!-- 匹配规则 -->
        <div v-if="formData.couponType === CouponType.UNIVERSAL && formData.useOn === UseOnType.MOVIE_TICKET" class="form-grid">
          <ElFormItem label="匹配规则" class="form-item-full">
            <ElSelect
              v-model="formData.priceRule!.matchRule"
              :disabled="pageMode === 'edit' || pageMode === 'view'"
              placeholder="请选择匹配规则"
              style="width: 300px"
            >
              <ElOption
                v-for="option in MATCH_RULE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
            <ElText type="info" class="ml-2">
              <ElIcon><InfoFilled /></ElIcon>
              {{ formData.priceRule!.matchRule === 0 ? '优惠补差时可添加多条规则' : '最低票价时只能有一条规则' }}
            </ElText>
          </ElFormItem>
        </div>

        <!-- 价格层级规则 -->
        <div class="price-tiers">
          <div v-if="formData.couponType === CouponType.UNIVERSAL" class="price-tier-header">
            <span class="tier-title">价格层级规则</span>
            <ElButton
              v-if="canAddMultiplePriceTiers && formData.priceRule!.priceTiers.length < maxPriceTiers && pageMode !== 'edit' && pageMode !== 'view'"
              type="primary"
              :icon="Plus"
              size="small"
              @click="addPriceTier"
            >
              添加规则
            </ElButton>
          </div>

          <div
            v-for="(tier, index) in formData.priceRule!.priceTiers"
            :key="index"
            class="price-tier-item"
          >
            <div class="tier-header">
              <span class="tier-index">规则 {{ index + 1 }}</span>
              <ElButton
                v-if="formData.priceRule!.priceTiers.length > 1 && pageMode !== 'edit' && pageMode !== 'view'"
                type="danger"
                :icon="Delete"
                size="small"
                text
                @click="removePriceTier(index)"
              >
                删除
              </ElButton>
            </div>

            <!-- 满减券规则 -->
            <div v-if="formData.couponType === 0" class="tier-content">
              <ElFormItem label="满减金额">
                <ElInputNumber
                  :model-value="tier.priceOrigin / 100"
                  :disabled="pageMode === 'edit' || pageMode === 'view'"
                  :min="0"
                  :precision="2"
                  :step="1"
                  style="width: 100%"
                  @update:model-value="(val) => tier.priceOrigin = Math.round(val * 100)"
                >
                  <template #suffix>
                    元
                  </template>
                </ElInputNumber>
              </ElFormItem>
              <ElFormItem label="减免金额">
                <ElInputNumber
                  :model-value="tier.priceReduce / 100"
                  :disabled="pageMode === 'edit' || pageMode === 'view'"
                  :min="0"
                  :precision="2"
                  :step="1"
                  style="width: 100%"
                  @update:model-value="(val) => tier.priceReduce = Math.round(val * 100)"
                >
                  <template #suffix>
                    元
                  </template>
                </ElInputNumber>
              </ElFormItem>
            </div>

            <!-- 减至券规则 -->
            <div v-else-if="formData.couponType === 1" class="tier-content">
              <ElFormItem label="减至金额">
                <ElInputNumber
                  :model-value="tier.priceReduce / 100"
                  :disabled="pageMode === 'edit' || pageMode === 'view'"
                  :min="0"
                  :precision="2"
                  :step="1"
                  style="width: 100%"
                  @update:model-value="(val) => tier.priceReduce = Math.round(val * 100)"
                >
                  <template #suffix>
                    元
                  </template>
                </ElInputNumber>
              </ElFormItem>
            </div>

            <!-- 通兑券规则 -->
            <div v-else-if="formData.couponType === 2" class="tier-content">
              <!-- 电影票 + 通兑券 + 最低价：只显示减免金额 -->
              <template v-if="formData.useOn === 0 && formData.priceRule?.matchRule === 1">
                <div class="special-rule-tip">
                  <ElText type="info" size="small">
                    <ElIcon><InfoFilled /></ElIcon>
                    电影票通兑券在最低价模式下，只需配置减免金额
                  </ElText>
                </div>
                <ElFormItem label="减免金额" style="width: 300px;">
                  <ElInputNumber
                    :model-value="tier.priceReduce / 100"
                    :disabled="pageMode === 'edit' || pageMode === 'view'"
                    :min="0"
                    :precision="2"
                    :step="1"
                    style="width: 100%"
                    @update:model-value="(val) => tier.priceReduce = Math.round(val * 100)"
                  >
                    <template #suffix>
                      元
                    </template>
                  </ElInputNumber>
                </ElFormItem>
              </template>

              <!-- 其他情况：显示完整的通兑券配置 -->
              <template v-else>
                <ElFormItem label="最小金额">
                  <ElInputNumber
                    :model-value="tier.priceOrigin / 100"
                    :disabled="pageMode === 'edit' || pageMode === 'view'"
                    :min="0"
                    :precision="2"
                    :step="1"
                    style="width: 100%"
                    @update:model-value="(val) => tier.priceOrigin = Math.round(val * 100)"
                  >
                    <template #suffix>
                      元
                    </template>
                  </ElInputNumber>
                </ElFormItem>
                <ElFormItem label="最大金额">
                  <ElInputNumber
                    :model-value="tier.priceReduce / 100"
                    :disabled="pageMode === 'edit' || pageMode === 'view'"
                    :min="0"
                    :precision="2"
                    :step="1"
                    style="width: 100%"
                    @update:model-value="(val) => tier.priceReduce = Math.round(val * 100)"
                  >
                    <template #suffix>
                      元
                    </template>
                  </ElInputNumber>
                </ElFormItem>
                <ElFormItem label="补差金额">
                  <ElInputNumber
                    :model-value="tier.priceDiff / 100"
                    :disabled="pageMode === 'edit' || pageMode === 'view'"
                    :min="0"
                    :precision="2"
                    :step="1"
                    style="width: 100%"
                    @update:model-value="(val) => tier.priceDiff = Math.round(val * 100)"
                  >
                    <template #suffix>
                      元
                    </template>
                  </ElInputNumber>
                </ElFormItem>
              </template>
            </div>

            <!-- 折扣券规则 -->
            <div v-else-if="formData.couponType === 3" class="tier-content">
              <ElFormItem label="满减金额">
                <ElInputNumber
                  :model-value="tier.priceOrigin / 100"
                  :disabled="pageMode === 'edit' || pageMode === 'view'"
                  :min="0"
                  :precision="2"
                  :step="1"
                  style="width: 100%"
                  @update:model-value="(val) => tier.priceOrigin = Math.round(val * 100)"
                >
                  <template #suffix>
                    元
                  </template>
                </ElInputNumber>
              </ElFormItem>
              <ElFormItem label="折扣率">
                <ElInputNumber
                  v-model="tier.priceReduce"
                  :disabled="pageMode === 'edit' || pageMode === 'view'"
                  :min="0"
                  :max="100"
                  :precision="2"
                  style="width: 100%"
                >
                  <template #suffix>
                    %
                  </template>
                </ElInputNumber>
              </ElFormItem>
            </div>

            <!-- 多对一券规则 -->
            <div v-else-if="formData.couponType === 4" class="tier-content">
              <ElFormItem label="兑换数量">
                <ElInputNumber
                  v-model="tier.priceReduce"
                  :disabled="pageMode === 'edit' || pageMode === 'view'"
                  :min="1"
                  :precision="0"
                  style="width: 100%"
                >
                  <template #suffix>
                    张
                  </template>
                </ElInputNumber>
              </ElFormItem>
            </div>
          </div>
        </div>
      </div>

      <!-- 渠道规则 -->
      <div class="form-section">
        <div class="section-title">
          <h3>渠道规则</h3>
          <p>设置优惠券的适用渠道范围</p>
        </div>

        <div class="form-grid">
          <!-- 渠道范围 -->
          <ElFormItem label="渠道范围" class="form-item-full">
            <ElSelect
              v-model="formData.channelRule!.channelScope"
              placeholder="请选择渠道范围"
              style="width: 300px"
            >
              <ElOption
                v-for="option in CHANNEL_SCOPE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </ElFormItem>

          <!-- 指定渠道 -->
          <ElFormItem
            v-if="formData.channelRule!.channelScope === 1"
            label="适用渠道"
            class="form-item-full"
          >
            <ElSelect
              v-model="formData.channelRule!.channels"
              placeholder="请选择适用渠道"
              multiple
              collapse-tags
              collapse-tags-tooltip
              style="width: 100%"
              :loading="loading"
              value-key="channelId"
            >
              <ElOption
                v-for="option in channelOptions"
                :key="option.value"
                :label="option.label"
                :value="{ channelId: option.value, channelName: option.label }"
              />
            </ElSelect>
          </ElFormItem>
        </div>
      </div>

      <!-- 生成规则 -->
      <div class="form-section">
        {{ formData.generateRule }}
        <CouponGenerateType
          v-model:generate-type="formData.generateRule!.generateType"
          v-model:num="formData.generateRule!.num"
          v-model:batch-size="formData.generateRule!.batchSize"
        />
      </div>

      <!-- 结算规则 -->
      <div class="form-section">
        {{ formData.settleRule }}
        <CouponSettleMethod
          v-model:method="formData.settleRule!.method"
          v-model:priced="formData.settleRule!.priced"
        />
      </div>

      <!-- 有效期规则 -->
      <div class="form-section">
        <CouponValidityPeriod
          v-model:valid-scope="formData.periodRule!.validScope"
          v-model:start-time="formData.periodRule!.startTime"
          v-model:end-time="formData.periodRule!.endTime"
          v-model:overdue-day="formData.periodRule!.overdueDay"
        />
      </div>

      <!-- 备注信息 -->
      <div class="form-section">
        <div class="section-title">
          <h3>备注信息</h3>
          <p>添加优惠券的备注和说明信息</p>
        </div>

        <div class="form-grid">
          <ElFormItem label="备注" class="form-item-full">
            <ElInput
              v-model="formData.note"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              maxlength="200"
              show-word-limit
            />
          </ElFormItem>

          <ElFormItem label="说明" class="form-item-full">
            <ElInput
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入说明信息"
              maxlength="500"
              show-word-limit
            />
          </ElFormItem>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <ElButton @click="handleReset">
          重置
        </ElButton>
        <ElButton @click="emit('cancel')">
          取消
        </ElButton>
        <ElButton type="primary" :loading="loading" @click="handleSubmit">
          {{ pageMode === 'add' ? '创建优惠券' : '保存修改' }}
        </ElButton>
      </div>
    </ElForm>
    <pre>
      {{ JSON.stringify(formData, null, 2) }}
    </pre>
  </div>
</template>

<style scoped>
.coupon-base-config {
  padding: 20px;
  background: var(--el-bg-color);
  border-radius: 8px;
}

.config-form {
  max-width: 1000px;
  margin: 0 auto;
}

/* 表单分组样式 */
.form-section {
  margin-bottom: 32px;
  padding: 24px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.form-section:last-child {
  margin-bottom: 0;
}

/* 禁用状态的表单区域 */
.form-section-disabled {
  background: var(--el-fill-color-lighter) !important;
  border-color: var(--el-border-color-lighter) !important;
  opacity: 0.8;
  position: relative;
}

.form-section-disabled::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  pointer-events: none;
  z-index: 1;
}

.form-section-disabled :deep(.el-input__wrapper),
.form-section-disabled :deep(.el-select__wrapper),
.form-section-disabled :deep(.el-input-number__wrapper) {
  background-color: var(--el-disabled-bg-color) !important;
  cursor: not-allowed !important;
}

.form-section-disabled :deep(.el-input__inner),
.form-section-disabled :deep(.el-select__input),
.form-section-disabled :deep(.el-input-number__inner) {
  color: var(--el-disabled-text-color) !important;
  cursor: not-allowed !important;
}

/* 编辑警告样式 */
.edit-warning {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
  padding: 8px 12px;
  background: var(--el-color-warning-light-9);
  border: 1px solid var(--el-color-warning-light-7);
  border-radius: 4px;
  font-size: 12px;
}

.section-title {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--el-color-primary);
}

.section-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.section-title p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

/* 表单网格布局 */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  align-items: start;
}

.form-item-full {
  grid-column: 1 / -1;
}

/* 价格层级规则样式 */
.price-tiers {
  margin-top: 20px;
}

.price-tier-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: var(--el-fill-color-light);
  border-radius: 6px;
}

.tier-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.price-tier-item {
  margin-bottom: 16px;
  padding: 16px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
}

.price-tier-item:last-child {
  margin-bottom: 0;
}

.tier-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.tier-index {
  font-weight: 500;
  color: var(--el-color-primary);
}

.tier-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.special-rule-tip {
  grid-column: 1 / -1;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: var(--el-color-info-light-9);
  border: 1px solid var(--el-color-info-light-7);
  border-radius: 4px;
}

.special-rule-tip .el-text {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 表单项样式 */
:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

:deep(.el-form-item__content) {
  line-height: 1.5;
}

/* 输入框样式 */
:deep(.el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary-light-7);
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-color-primary);
}

/* 选择器样式 */
:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select:hover .el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-primary-light-7);
}

:deep(.el-select.is-focused .el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-primary);
}

/* 数字输入框样式 */
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__wrapper) {
  border-radius: 6px;
}

/* 文本域样式 */
:deep(.el-textarea__inner) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.el-textarea__inner:hover) {
  border-color: var(--el-color-primary-light-7);
}

:deep(.el-textarea__inner:focus) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 1px var(--el-color-primary);
}

/* 操作按钮样式 */
.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding: 24px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.form-actions .el-button {
  min-width: 100px;
  border-radius: 6px;
  font-weight: 500;
}

/* 工具类 */
.ml-2 {
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .coupon-base-config {
    padding: 12px;
  }

  .form-section {
    padding: 16px;
    margin-bottom: 20px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .tier-content {
    grid-template-columns: 1fr;
  }

  .price-tier-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .tier-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .form-actions .el-button {
    min-width: auto;
  }
}

/* 动画效果 */
.form-section {
  animation: fadeInUp 0.3s ease-out;
}

.price-tier-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 悬停效果 */
.form-section:hover:not(.form-section-disabled) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

/* 禁用状态下不显示悬停效果 */
.form-section-disabled:hover {
  box-shadow: none;
}

.price-tier-item:hover {
  border-color: var(--el-color-primary-light-7);
  transition: border-color 0.3s ease;
}

/* 焦点样式增强 */
:deep(.el-form-item.is-error .el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger);
}

:deep(.el-form-item.is-error .el-textarea__inner) {
  border-color: var(--el-color-danger);
  box-shadow: 0 0 0 1px var(--el-color-danger);
}

/* 标签样式 */
:deep(.el-form-item__label::before) {
  content: '';
  width: 3px;
  height: 14px;
  background: var(--el-color-primary);
  border-radius: 2px;
  margin-right: 8px;
  display: inline-block;
  vertical-align: middle;
}

/* 必填项标记 */
:deep(.el-form-item.is-required .el-form-item__label::after) {
  content: '*';
  color: var(--el-color-danger);
  margin-left: 4px;
}

/* 信息提示样式 */
:deep(.el-text.el-text--info) {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}
</style>
