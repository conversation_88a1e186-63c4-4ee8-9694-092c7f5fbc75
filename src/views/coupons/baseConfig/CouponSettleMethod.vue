<script setup lang="ts">
import { ElFormItem, ElInputNumber, ElOption, ElRadio, ElRadioGroup, ElSelect, ElText } from 'element-plus'
import { computed } from 'vue'

// "method": 0,
//     "priced": {
//   "type": 0,
//       "money": 0
// }

interface Props {
  method?: 0 | 1 | 2 // 结算方式 0:定价规则 1:影院团购价 2.自定义结算 不能为空
  priced?: { // 自定义结算：method值为2，必传
    type: number // 计算方式0-固定价格 1-最低票价
    money: number // 定价金额(单位：分)
  }
}

interface Emits {
  (e: 'update:method', value: number): void
  (e: 'update:priced', value: { type: number, money: number }): void
}

const props = withDefaults(defineProps<Props>(), {
  method: 0,
  priced: {
    type: 0,
    money: 0,
  },
})

const emit = defineEmits<Emits>()

// 计算属性用于双向绑定
const methodModel = computed({
  get: () => props.method,
  set: (value: number) => emit('update:method', value),
})

const pricedModel = computed({
  get: () => props.priced,
  set: (value: { type: number, money: number }) => emit('update:priced', value),
})

// 定价金额（元）
const pricedMoneyYuan = computed({
  get: () => (props.priced?.money || 0) / 100,
  set: (value: number) => {
    const newPriced = { ...props.priced, type: props.priced?.type || 0, money: Math.round(value * 100) }
    emit('update:priced', newPriced)
  },
})

// 计算方式更新
function updatePricedType(type: number) {
  const newPriced = { ...props.priced, type, money: props.priced?.money || 0 }
  emit('update:priced', newPriced)
}
</script>

<template>
  <div class="coupon-settle-method">
    <div class="section-title">
      <h3>结算规则</h3>
      <p>设置优惠券的结算方式和比例</p>
    </div>

    <div class="form-grid">
      <ElFormItem label="结算方式" class="form-item-full">
        <ElRadioGroup v-model="methodModel">
          <ElRadio :value="0">
            <div class="radio-content">
              <div class="radio-title">
                定价规则
              </div>
              <div class="radio-desc">
                按照系统定价规则进行结算
              </div>
            </div>
          </ElRadio>
          <ElRadio :value="1">
            <div class="radio-content">
              <div class="radio-title">
                影院团购价
              </div>
              <div class="radio-desc">
                当存在未配置团购价的影城时，将使用定价规则中的价格进行结算
              </div>
            </div>
          </ElRadio>
          <ElRadio :value="2">
            <div class="radio-content">
              <div class="radio-title">
                自定义结算
              </div>
              <div class="radio-desc">
                自定义结算价格和计算方式
              </div>
            </div>
          </ElRadio>
        </ElRadioGroup>
      </ElFormItem>

      <!-- 自定义结算配置 -->
      <div v-if="method === 2" class="custom-settle-config">
        <ElFormItem label="计算方式">
          <ElSelect
            :model-value="priced?.type || 0"
            placeholder="请选择计算方式"
            style="width: 200px"
            @update:model-value="updatePricedType"
          >
            <ElOption label="固定价格" :value="0" />
            <ElOption label="最低票价" :value="1" />
          </ElSelect>
          <ElText type="info" class="form-tip">
            选择结算价格的计算方式
          </ElText>
        </ElFormItem>

        <ElFormItem label="定价金额">
          <ElInputNumber
            v-model="pricedMoneyYuan"
            :min="0"
            :max="9999.99"
            :precision="2"
            :step="0.01"
            controls-position="right"
            style="width: 200px"
          >
            <template #append>
              元
            </template>
          </ElInputNumber>
          <ElText type="info" class="form-tip">
            {{ priced?.type === 0 ? '固定价格金额' : '最低票价基础上的金额' }}
          </ElText>
        </ElFormItem>
      </div>
    </div>
  </div>
</template>

<style scoped>
.coupon-settle-method {
  width: 100%;
}

.section-title {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--el-color-primary);
}

.section-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.section-title p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  align-items: start;
}

.form-item-full {
  grid-column: 1 / -1;
}

.custom-settle-config {
  grid-column: 1 / -1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 16px;
  background: var(--el-fill-color-light);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.radio-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.radio-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.radio-desc {
  font-size: 12px;
  color: var(--el-text-color-regular);
  line-height: 1.4;
}

.form-tip {
  display: block;
  margin-top: 4px;
  font-size: 12px;
}

/* 表单项样式 */
:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* 单选按钮组样式 */
:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
}

:deep(.el-radio) {
  margin-right: 0;
  margin-bottom: 0;
  align-items: flex-start;
}

:deep(.el-radio__input) {
  margin-top: 2px;
}

/* 选择器样式 */
:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

/* 数字输入框样式 */
:deep(.el-input-number .el-input__wrapper) {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .custom-settle-config {
    grid-template-columns: 1fr;
  }
}
</style>
