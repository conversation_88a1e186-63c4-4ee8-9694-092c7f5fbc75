/**
 * 优惠券管理模块类型定义
 * 基于API文档：src/api/modules/coupons/优惠券.md
 */

// ==================== 基础类型 ====================

/** 使用状态枚举 */
export enum UseStatus {
  DISABLED = 0, // 关闭
  ENABLED = 1, // 开启
  DRAFT = 2, // 草稿
}

/** 生成状态枚举 */
export enum GenerateStatus {
  NOT_GENERATED = 0, // 未生成
  GENERATED = 1, // 已生成
}

/** 适用商品类型枚举 */
export enum UseOnType {
  MOVIE_TICKET = 0, // 影票
  GOODS = 1, // 卖品
  SHOW = 2, // 演出
  EXHIBITION = 3, // 展览
  ECOMMERCE = 4, // 电商
}

/**
 * 优惠券类型枚举
 * @description 0-满减，1-减至，2-通兑，3-折扣，4-多对一
 */
export enum CouponType {
  FULL_REDUCTION = 0, // 满减
  REDUCTION_TO = 1, // 减至
  UNIVERSAL = 2, // 通兑
  DISCOUNT = 3, // 折扣
  MULTI_TO_ONE = 4, // 多对一
}

/** 服务费减免枚举 */
export enum ServiceFeeReduction {
  REDUCE = 0, // 减免
  NO_REDUCE = 1, // 不减免
}

/** 匹配规则枚举 */
export enum MatchRule {
  DISCOUNT_SUPPLEMENT = 0, // 优惠补差
  LOWEST_PRICE = 1, // 最低票价
}

/** 渠道范围枚举 */
export enum ChannelScope {
  SPECIFIED = 1, // 指定渠道
  ALL = 2, // 全部渠道
}

// ==================== 复杂对象类型 ====================

/** 价格层级规则 */
export interface PriceTier {
  priceOrigin: number // 原始价格(单位：分)
  priceReduce: number // 减免金额(单位：分)
  priceDiff: number // 价格差值(单位：分)
}

/** 价格规则 */
export interface PriceRule {
  priceTiers: PriceTier[] // 优惠规则数组
  matchRule: number // 匹配规则 0 优惠补差 1 最低票价
}

/** 渠道信息 */
export interface Channel {
  channelId: string // 渠道ID
  channelName: string // 渠道名称
}

/** 渠道规则 */
export interface ChannelRule {
  channelScope: number // 渠道范围 1-指定渠道 2-全部渠道
  channels: Channel[] // 适用渠道列表
}

/** 生成规则 */
export interface GenerateRule {
  generateType: number // 生成类型：0手动，1自动
  num: number // 生成数量
  batchSize?: number // 批次大小
}

/** 结算规则 */
export interface SettleRule {
  method?: 0 | 1 | 2 // 结算方式 0:定价规则 1:影院团购价 2.自定义结算 不能为空
  priced?: { // 自定义结算：method值为2，必传
    type: number // 计算方式0-固定价格 1-最低票价
    money: number // 定价金额(单位：分)
  }
}

/** 时间有效规则 */
export interface PeriodRule {
  validScope: number // 有效期类型：0固定时间，1相对时间
  startTime?: number // 开始时间（固定时间模式）
  endTime?: number // 结束时间（固定时间模式）
  overdueDay?: number // 有效天数（相对时间模式）
}

// ==================== 主要数据类型 ====================

/** 优惠券信息 - 根据API文档定义 */
export interface Coupon {
  id: string // MongoDB自动生成的ID
  restrictionId?: string // 限制规则ID
  name: string // 优惠券名称
  useOn: UseOnType // 适用商品：影票0，卖品1，演出2，展览3、电商4
  reduction: ServiceFeeReduction // 服务费减免：减免0，不减免1
  userServiceFee: number // 用户服务费(单位：分)
  note?: string // 备注信息
  remark?: string // 备注说明
  couponType: CouponType // 优惠券类型: 满减0，减至1，通兑2，折扣3，多对一4
  priceRule?: PriceRule // 价格规则
  channelRule?: ChannelRule // 适用对象
  generateRule?: GenerateRule // 生成规则
  settleRule?: SettleRule // 结算规则(影院结算)
  periodRule?: PeriodRule // 时间有效规则
  createTime: number // 创建时间
  updatedTime: number // 更新时间
  bindCount: number // 券已绑定数
  usedCount: number // 券已使用数
  useStatus: UseStatus // 使用状态，0关闭，1开启，2 草稿
  generateStatus: GenerateStatus // 生成状态，0未生成，1已生成
  deleted: boolean // 是否删除
}

// ==================== API请求参数类型 ====================

/** 优惠券列表查询参数 */
export interface CouponListParams {
  page?: number // 页码，从0开始
  size?: number // 每页数量
  id?: string // 优惠券ID
  name?: string // 优惠券名称
  useOn?: UseOnType // 适用类型
  couponType?: CouponType // 优惠券类型
  cinemaScope?: number // 影院范围
  cinemaId?: string // 影院ID
  startTime?: string // 开始时间
  endTime?: string // 结束时间
  useStatus?: UseStatus // 使用状态
  num?: number // 数量
  generateType?: number // 生成类型
  channelId?: string // 渠道ID
  productId?: string // 产品ID
  channelCode?: string // 渠道代码
}

/** 优惠券创建参数 */
export interface CreateCouponParams {
  name: string // 优惠券名称
  useOn: UseOnType // 适用类型
  couponType: CouponType // 优惠券类型
  reduction: ServiceFeeReduction // 服务费减免
  userServiceFee: number // 用户服务费
  priceRule?: PriceRule // 价格规则
  channelRule?: ChannelRule // 渠道规则
  generateRule: GenerateRule // 生成规则
  settleRule?: SettleRule // 结算规则
  periodRule: PeriodRule // 时间规则
  note?: string // 备注
  remark?: string // 说明
}

/** 优惠券更新参数 */
export interface UpdateCouponParams extends CreateCouponParams {
  id: string // 优惠券ID
}

/** 优惠券状态变更参数 */
export interface CouponStatusParams {
  id: string // 优惠券ID
  useStatus: UseStatus // 新的使用状态
}

/** 绑定优惠券参数 */
export interface BindCouponParams {
  id: string // 优惠券ID
  userIds?: string[] // 用户ID列表
  mobiles: string[] // 手机号列表
  num?: number // 绑定数量
  bindType?: number // 绑定类型
  codeId: string // 券码ID
}

// ==================== API响应类型 ====================

/** 基础响应类型 */
export interface BaseResponse<T = any> {
  code: number
  data: T
  msg?: string
  success?: boolean
}

/** 分页响应类型 */
export interface PageResponse<T = any> {
  total: number
  content?: T[]
  result?: T[]
  records?: T[]
  list?: T[]
}

/** 优惠券列表响应 */
export interface CouponListResponse extends PageResponse<Coupon> {}

/** 优惠券详情响应 */
export type CouponDetailResponse = Coupon

// ==================== 表单相关类型 ====================

/** 搜索表单数据 */
export interface SearchFormData {
  name?: string // 优惠券名称
  useOn?: UseOnType // 适用类型
  couponType?: CouponType // 优惠券类型
  useStatus?: UseStatus // 使用状态
  dateRange?: [string, string] // 时间范围
  cinemaId?: string // 影院ID
  channelCode?: string // 渠道代码
}

/** 分页配置 */
export interface PaginationConfig {
  page: number // 当前页码
  size: number // 每页数量
  total: number // 总数量
}

// ==================== 组件Props类型 ====================

/** 表格组件Props */
export interface CouponTableProps {
  data: Coupon[]
  loading?: boolean
}

/** 搜索表单组件Props */
export interface CouponSearchFormProps {
  loading?: boolean
}

/** 详情对话框组件Props */
export interface CouponDetailDialogProps {
  modelValue: boolean
  coupon: Coupon | null
}

// ==================== 事件类型 ====================

/** 表格组件事件 */
export interface CouponTableEmits {
  'view-detail': (coupon: Coupon) => void
  'edit': (coupon: Coupon) => void
  'delete': (coupon: Coupon) => void
  'status-change': (coupon: Coupon, newValue: boolean) => void
  'bind': (coupon: Coupon) => void
  'view-codes': (coupon: Coupon) => void
}

/** 搜索表单组件事件 */
export interface CouponSearchFormEmits {
  search: (params: SearchFormData) => void
  reset: () => void
  export: () => void
}

/** 详情对话框组件事件 */
export interface CouponDetailDialogEmits {
  'update:modelValue': (value: boolean) => void
  'edit': (coupon: Coupon) => void
  'export': (coupon: Coupon) => void
  'share': (coupon: Coupon) => void
}
