<script setup lang="ts">
import { InfoFilled } from '@element-plus/icons-vue'
import { ElFormItem, ElIcon, ElInputNumber, ElRadio, ElRadioGroup, ElTooltip } from 'element-plus'
import { computed } from 'vue'

interface Props {
  generateType?: number
  num?: number
}

interface Emits {
  (e: 'update:generateType', value: number): void
  (e: 'update:num', value: number): void
}

const props = withDefaults(defineProps<Props>(), {
  generateType: 1,
  num: 100,
})

const emit = defineEmits<Emits>()

// 计算属性用于双向绑定
const generateTypeModel = computed({
  get: () => props.generateType,
  set: (value: number) => emit('update:generateType', value),
})

const numModel = computed({
  get: () => props.num,
  set: (value: number) => emit('update:num', value),
})
</script>

<template>
  <div class="coupon-generate-type">
    <div class="section-title">
      <h3>生成规则</h3>
      <p>设置优惠券的生成方式和数量</p>
    </div>

    <div class="form-grid">
      <ElFormItem label="生成方式">
        <template #label>
          <div class="label-with-tooltip">
            <span>生成方式</span>
            <ElTooltip content="券新建完成后不可更改，请谨慎选择" placement="top">
              <ElIcon class="tooltip-icon">
                <InfoFilled />
              </ElIcon>
            </ElTooltip>
          </div>
        </template>
        <ElRadioGroup v-model="generateTypeModel">
          <ElTooltip content="适用场景：一次性卖给某个大客户多少张券" placement="top">
            <ElRadio :value="1">
              一次性生成
            </ElRadio>
          </ElTooltip>
          <ElTooltip content="适用场景：用户通过线上营销活动获得优惠券" placement="top">
            <ElRadio :value="0">
              需要时生成
            </ElRadio>
          </ElTooltip>
        </ElRadioGroup>
      </ElFormItem>

      <ElFormItem :label="generateType === 1 ? '生成数量' : '生成张数上限'">
        <ElInputNumber
          v-model="numModel"
          :min="1"
          :max="10000"
          :step="1"
          controls-position="right"
          placeholder="请输入生成数量"
          style="width: 100%"
        />
      </ElFormItem>
    </div>
  </div>
</template>

<style scoped>
.coupon-generate-type {
  width: 100%;
}

.section-title {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--el-color-primary);
}

.section-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.section-title p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  align-items: start;
}

.label-with-tooltip {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tooltip-icon {
  font-size: 14px;
  color: var(--el-color-info);
  cursor: help;
}

.form-tip {
  display: block;
  margin-top: 4px;
  font-size: 12px;
}

/* 表单项样式 */
:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* 单选按钮组样式 */
:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.el-radio) {
  margin-right: 0;
  margin-bottom: 0;
}

/* 数字输入框样式 */
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__wrapper) {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  :deep(.el-radio-group) {
    flex-direction: column;
  }
}
</style>
