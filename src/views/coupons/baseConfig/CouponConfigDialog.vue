<script setup lang="ts">
// 类型导入
import type { CreateCouponParams } from '@/views/coupons/baseConfig/types'
import { ElDialog, ElMessage } from 'element-plus'

import { computed, ref } from 'vue'

import { getCouponDetail } from '@/api/modules/coupons'

// 导入基础配置组件
import CouponBaseConfig from './index.vue'

defineOptions({
  name: 'CouponConfigDialog',
})

const props = withDefaults(defineProps<Props>(), {
  pageMode: 'add',
  couponId: '',
  title: '',
  width: '1200px',
  destroyOnClose: true,
  closeOnClickModal: false,
})

const emit = defineEmits<Emits>()

// ==================== Props & Emits ====================

interface Props {
  modelValue: boolean
  pageMode?: 'add' | 'edit' | 'view'
  couponId?: string
  title?: string
  width?: string | number
  destroyOnClose?: boolean
  closeOnClickModal?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', data: CreateCouponParams): void
  (e: 'next-step', couponId: string): void
  (e: 'success', data: any): void
  (e: 'cancel'): void
  (e: 'close'): void
}

// ==================== 响应式数据 ====================

const couponData = ref<CreateCouponParams | undefined>()

// 基础配置组件引用
const baseConfigRef = ref()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 动态标题
const dialogTitle = computed(() => {
  if (props.title) { return props.title }

  switch (props.pageMode) {
    case 'add': return '创建优惠券'
    case 'edit': return '编辑优惠券'
    case 'view': return '查看优惠券'
    default: return '优惠券配置'
  }
})

// 是否只读模式
const isReadonly = computed(() => props.pageMode === 'view')

// ==================== 事件处理 ====================

/**
 * 处理表单提交
 */
function handleSubmit(data: CreateCouponParams) {
  emit('submit', data)
}

/**
 * 处理下一步操作（创建成功后）
 */
function handleNextStep(couponId: string) {
  emit('next-step', couponId)
  emit('success', { couponId })
}

/**
 * 处理取消操作
 */
function handleCancel() {
  emit('cancel')
  dialogVisible.value = false
}

/**
 * 处理对话框关闭
 */
function handleClose() {
  emit('close')
}

/**
 * 处理对话框打开前的操作
 */
function handleOpen() {
  // 对话框打开时的逻辑
  if (props.couponId && props.pageMode === 'edit') {
    // 加载编辑数据
    console.log('加载编辑数据:', props.couponId)
    getCouponDetail(props.couponId).then((res) => {
      const { code, data, msg } = res
      console.log({ code, data, msg })
      if (code === 0) {
        couponData.value = data
      }
      else {
        ElMessage.error(msg || '获取优惠券详情失败')
      }
    })
  }
}

/**
 * 处理对话框关闭后的操作
 */
function handleClosed() {
  // 对话框关闭后的清理逻辑
}

/**
 * 外部调用：提交表单
 */
function submit() {
  return baseConfigRef.value?.handleSubmit()
}

/**
 * 外部调用：重置表单
 */
function reset() {
  return baseConfigRef.value?.handleReset()
}

/**
 * 外部调用：验证表单
 */
function validate() {
  return baseConfigRef.value?.validateForm()
}

/**
 * 外部调用：获取表单数据
 */
function getFormData() {
  return baseConfigRef.value?.formData
}

// ==================== 暴露方法 ====================

defineExpose({
  submit,
  reset,
  validate,
  getFormData,
})
onMounted(() => {
  console.log('对话框已挂载')
})
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="width"
    :destroy-on-close="destroyOnClose"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="!isReadonly"
    :show-close="true"
    draggable
    align-center
    class="coupon-config-dialog"
    @open="handleOpen"
    @close="handleClose"
    @closed="handleClosed"
  >
    <!-- 对话框内容 -->
    <div class="dialog-content">
      {{ couponData }}
      <CouponBaseConfig
        ref="baseConfigRef"
        :page-mode="pageMode"
        :coupon-data="couponData"
        @submit="handleSubmit"
        @next-step="handleNextStep"
        @cancel="handleCancel"
        @close="handleCancel"
      />
    </div>

    <!-- 自定义底部按钮（如果需要的话） -->
    <template v-if="false" #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">
          取消
        </ElButton>
        <ElButton v-if="!isReadonly" type="primary" @click="submit">
          {{ pageMode === 'add' ? '创建' : '保存' }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.coupon-config-dialog {
  --el-dialog-border-radius: 12px;
}

/* 对话框内容样式 */
.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0;
}

/* 自定义滚动条 */
.dialog-content::-webkit-scrollbar {
  width: 6px;
}

.dialog-content::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark);
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb:hover {
  background: var(--el-fill-color-darker);
}

/* 对话框底部按钮样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0 0 0;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 对话框头部样式优化 */
:deep(.el-dialog__header) {
  padding: 20px 24px 16px 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color-page);
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

:deep(.el-dialog__headerbtn) {
  top: 20px;
  right: 24px;
  width: 32px;
  height: 32px;
}

:deep(.el-dialog__close) {
  font-size: 16px;
  color: var(--el-text-color-regular);
}

:deep(.el-dialog__close:hover) {
  color: var(--el-color-primary);
}

/* 对话框主体样式 */
:deep(.el-dialog__body) {
  padding: 0;
  background: var(--el-bg-color);
}

/* 响应式设计 */
@media (max-width: 1400px) {
  :deep(.el-dialog) {
    width: 90% !important;
    max-width: 1000px;
  }
}

@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto;
  }

  .dialog-content {
    max-height: 80vh;
  }

  :deep(.el-dialog__header) {
    padding: 16px 20px 12px 20px;
  }

  :deep(.el-dialog__headerbtn) {
    top: 16px;
    right: 20px;
  }
}

/* 动画效果 */
:deep(.el-dialog) {
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 遮罩层样式 */
:deep(.el-overlay) {
  backdrop-filter: blur(2px);
}

/* 拖拽时的样式 */
:deep(.el-dialog.is-dragging) {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}
</style>
