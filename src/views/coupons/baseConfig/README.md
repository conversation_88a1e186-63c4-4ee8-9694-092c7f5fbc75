# 优惠券基础配置功能

基于API文档实现的完整优惠券基础配置功能，支持优惠券的创建、编辑、查看和管理。

## 🎯 功能概述

### 核心功能
- **优惠券创建**: 支持多种类型优惠券的创建配置
- **优惠券编辑**: 完整的编辑功能，支持所有字段修改
- **优惠券查看**: 详细的优惠券信息展示
- **优惠券管理**: 列表展示、搜索、筛选等管理功能

### 支持的优惠券类型
- **满减券** (couponType: 0): 满足条件减免金额
- **减至券** (couponType: 1): 减至指定金额
- **通兑券** (couponType: 2): 通用兑换券
- **折扣券** (couponType: 3): 按比例折扣
- **多对一券** (couponType: 4): 多商品对一优惠

### 适用商品类型
- **影票** (useOn: 0): 电影票优惠券
- **卖品** (useOn: 1): 商品优惠券
- **演出** (useOn: 2): 演出票优惠券
- **展览** (useOn: 3): 展览票优惠券

## 📁 文件结构

```
src/views/coupons/baseConfig/
├── 🔧 核心组件
│   ├── CouponConfigDialog.vue          # 主配置对话框组件 ⭐
│   ├── CouponGenerateType.vue          # 生成类型配置组件
│   ├── CouponSettleMethod.vue          # 结算方式配置组件
│   ├── CouponValidityPeriod.vue        # 有效期配置组件
│   └── index.vue                       # 基础配置页面
├── 🧩 子组件
│   └── components/
│       ├── CouponTable.vue             # 优惠券列表表格
│       ├── CouponSearchForm.vue        # 搜索表单组件
│       ├── CouponDetailCard.vue        # 详情卡片组件
│       └── CouponDetailDialog.vue      # 详情对话框组件
├── 📊 数据层
│   ├── types/index.ts                  # 类型定义
│   ├── constants/index.ts              # 常量定义
│   └── utils/index.ts                  # 工具函数
└── 📚 文档
    ├── README.md                       # 本文档
    └── DATA_STRUCTURE_GUIDE.md         # 数据结构指南
```

## 🚀 核心组件

### 1. **CouponConfigDialog.vue** ⭐
主要的优惠券配置对话框组件

#### Props
```typescript
interface Props {
  modelValue: boolean                    // 对话框显示状态
  pageMode?: 'add' | 'edit' | 'view'    // 页面模式
  couponId?: string                     // 优惠券ID（编辑/查看时）
  title?: string                        // 自定义标题
  width?: string | number               // 对话框宽度
}
```

#### Events
```typescript
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', data: CreateCouponParams): void
  (e: 'success', data: any): void
  (e: 'cancel'): void
}
```

#### 使用示例
```vue
<template>
  <CouponConfigDialog
    v-model="dialogVisible"
    :page-mode="pageMode"
    :coupon-id="editCouponId"
    @submit="handleSubmit"
    @success="handleSuccess"
  />
</template>
```

### 2. **CouponTable.vue**
优惠券列表表格组件

#### 功能特性
- 完整的表格展示
- 排序和筛选功能
- 操作按钮（编辑、删除、查看等）
- 分页支持

#### 使用示例
```vue
<template>
  <CouponTable
    :data="couponList"
    :loading="loading"
    @edit="handleEdit"
    @delete="handleDelete"
    @view="handleView"
  />
</template>
```

### 3. **CouponSearchForm.vue**
搜索表单组件

#### 功能特性
- 多条件搜索
- 高级搜索展开/收起
- 表单重置功能
- 实时搜索

## 🎨 使用方式

### 在页面中使用
```vue
<template>
  <div class="coupon-management">
    <!-- 搜索表单 -->
    <CouponSearchForm @search="handleSearch" />
    
    <!-- 操作按钮 -->
    <div class="actions">
      <ElButton type="primary" @click="openCreateDialog">
        创建优惠券
      </ElButton>
    </div>
    
    <!-- 优惠券列表 -->
    <CouponTable
      :data="couponList"
      :loading="loading"
      @edit="handleEdit"
      @delete="handleDelete"
    />
    
    <!-- 配置对话框 -->
    <CouponConfigDialog
      v-model="dialogVisible"
      :page-mode="dialogMode"
      :coupon-id="editCouponId"
      @submit="handleSubmit"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CouponConfigDialog from './CouponConfigDialog.vue'
import CouponTable from './components/CouponTable.vue'
import CouponSearchForm from './components/CouponSearchForm.vue'

const dialogVisible = ref(false)
const dialogMode = ref('add')
const editCouponId = ref('')

function openCreateDialog() {
  dialogMode.value = 'add'
  editCouponId.value = ''
  dialogVisible.value = true
}

function handleEdit(coupon) {
  dialogMode.value = 'edit'
  editCouponId.value = coupon.id
  dialogVisible.value = true
}

function handleSubmit(data) {
  // 处理提交逻辑
  console.log('提交数据:', data)
}
</script>
```

## 📊 数据结构

### 优惠券基础信息
```typescript
interface Coupon {
  id: string                    // 优惠券ID
  name: string                  // 优惠券名称
  couponType: number            // 优惠券类型
  useOn: number                 // 适用商品
  status: number                // 状态
  createTime: string            // 创建时间
  // ... 其他字段
}
```

### 创建优惠券参数
```typescript
interface CreateCouponParams {
  name: string                  // 优惠券名称
  couponType: number            // 优惠券类型
  useOn: number                 // 适用商品
  priceRule: PriceRule          // 价格规则
  validityPeriod: ValidityPeriod // 有效期
  generateType: GenerateType    // 生成类型
  settleMethod: SettleMethod    // 结算方式
  // ... 其他配置
}
```

详细的数据结构说明请参考：`DATA_STRUCTURE_GUIDE.md`

## 🔧 工具函数

### 格式化函数
```typescript
// 格式化金额
function formatAmount(amount: number): string

// 获取优惠券类型描述
function getCouponTypeDesc(type: number): string

// 获取适用商品描述
function getUseOnDesc(useOn: number): string

// 适配优惠券列表数据
function adaptCouponList(data: any[]): Coupon[]
```

### 导出功能
```typescript
// 导出优惠券数据为CSV
function exportCouponsToCSV(coupons: Coupon[]): void

// 下载CSV文件
function downloadCSV(content: string, filename: string): void
```

## 🎯 最佳实践

### 1. **组件使用**
```vue
<!-- 推荐：明确指定所有必要属性 -->
<CouponConfigDialog
  v-model="dialogVisible"
  :page-mode="pageMode"
  :coupon-id="couponId"
  @submit="handleSubmit"
/>

<!-- 不推荐：缺少必要属性 -->
<CouponConfigDialog v-model="dialogVisible" />
```

### 2. **数据处理**
```typescript
// 推荐：使用类型安全的数据处理
function handleSubmit(data: CreateCouponParams) {
  // 数据已经是正确的格式，可直接提交
  submitToAPI(data)
}

// 不推荐：手动构建数据结构
function handleSubmit(formData: any) {
  const data = {
    name: formData.name,
    // 手动映射字段...
  }
}
```

### 3. **错误处理**
```typescript
// 推荐：完整的错误处理
try {
  const response = await createCoupon(data)
  if (response.code === 0) {
    ElMessage.success('创建成功')
  } else {
    ElMessage.error(response.msg)
  }
} catch (error) {
  console.error('创建失败:', error)
  ElMessage.error('网络错误，请重试')
}
```

## 📱 响应式设计

- **桌面端**: 完整功能展示，多列布局
- **平板端**: 自适应列数，保持可读性
- **移动端**: 单列布局，优化触摸交互

## 🔗 相关功能

- **限制条件配置**: `../restriction/` - 优惠券使用限制条件配置
- **优惠券详情**: `../detail/` - 优惠券详情页面
- **优惠券列表**: `../index.vue` - 优惠券管理主页面

## 📞 技术支持

如有问题或建议，请：

1. 查看 `DATA_STRUCTURE_GUIDE.md` 了解数据结构
2. 检查控制台错误信息
3. 确认API接口是否正常
4. 联系开发团队

---

**功能状态**: ✅ 已完成  
**最后更新**: 2024年  
**版本**: v1.0.0
