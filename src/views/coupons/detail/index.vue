<script setup lang="ts">
// 类型导入
import type { Coupon } from '../baseConfig/types'
import { ArrowLeft, Download, Edit, Share } from '@element-plus/icons-vue'
import { ElButton, ElCard, ElMessage, ElSkeleton } from 'element-plus'
import { onMounted, ref } from 'vue'

import { useRoute, useRouter } from 'vue-router'

// API导入
import { getCouponDetail } from '@/api/modules/coupons'

// 组件导入
import CouponDetailCard from '@/views/coupons/baseConfig/components/CouponDetailCard.vue'

// 工具函数导入
import { adaptCouponData, downloadCSV, exportCouponsToCSV, formatAmount, getUseOnDesc } from '../baseConfig/utils'

defineOptions({
  name: 'CouponDetail',
})

// ==================== 路由 ====================

const route = useRoute()
const router = useRouter()

// ==================== 响应式数据 ====================

const coupon = ref<Coupon | null>(null)
const loading = ref(false)

// ==================== 生命周期 ====================

onMounted(() => {
  const couponId = route.params.id as string
  if (couponId) {
    fetchCouponDetail(couponId)
  }
  else {
    ElMessage.error('优惠券ID不能为空')
    router.back()
  }
})

// ==================== 数据获取 ====================

// 获取优惠券详情
async function fetchCouponDetail(id: string) {
  try {
    loading.value = true
    const response = await getCouponDetail(id)
    const { data, code, msg } = response

    if (code === 0) {
      coupon.value = adaptCouponData(data)
    }
    else {
      ElMessage.error(msg || '获取优惠券详情失败')
      router.back()
    }
  }
  catch (error) {
    console.error('获取优惠券详情失败:', error)
    ElMessage.error('获取优惠券详情失败')
    router.back()
  }
  finally {
    loading.value = false
  }
}

// ==================== 事件处理 ====================

// 返回列表
function handleBack() {
  router.back()
}

// 编辑优惠券
function handleEdit() {
  if (coupon.value) {
    ElMessage.info('编辑功能开发中...')
    console.log('编辑优惠券:', coupon.value)
  }
}

// 导出优惠券
function handleExport() {
  if (coupon.value) {
    const csvContent = exportCouponsToCSV([coupon.value])
    const filename = `coupon_${coupon.value.id}_${new Date().toISOString().slice(0, 10)}.csv`
    downloadCSV(csvContent, filename)
    ElMessage.success('导出成功')
  }
}

// 分享优惠券
function handleShare() {
  if (coupon.value) {
    // 复制优惠券信息到剪贴板
    const shareText = `优惠券：${coupon.value.name}\n用户服务费：${formatAmount(coupon.value.userServiceFee)}\n适用类型：${getUseOnDesc(coupon.value.useOn)}\n优惠券ID：${coupon.value.id}`

    if (navigator.clipboard) {
      navigator.clipboard.writeText(shareText).then(() => {
        ElMessage.success('优惠券信息已复制到剪贴板')
      }).catch(() => {
        ElMessage.error('复制失败，请手动复制')
      })
    }
    else {
      ElMessage.info('分享功能开发中...')
    }
  }
}
</script>

<template>
  <div class="coupon-detail-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <ElButton :icon="ArrowLeft" @click="handleBack">
          返回列表
        </ElButton>
        <div class="header-title">
          <h1 class="page-title">
            优惠券详情
          </h1>
          <p v-if="coupon" class="page-subtitle">
            {{ coupon.name }}
          </p>
        </div>
      </div>

      <div v-if="coupon" class="header-actions">
        <ElButton :icon="Share" @click="handleShare">
          分享
        </ElButton>
        <ElButton :icon="Download" @click="handleExport">
          导出
        </ElButton>
        <ElButton type="primary" :icon="Edit" @click="handleEdit">
          编辑
        </ElButton>
      </div>
    </div>

    <!-- 详情内容 -->
    <div class="detail-content">
      <ElCard v-if="loading" class="loading-card">
        <ElSkeleton :rows="10" animated />
      </ElCard>

      <CouponDetailCard
        v-else-if="coupon"
        :coupon="coupon"
        @edit="handleEdit"
        @export="handleExport"
        @share="handleShare"
      />

      <ElCard v-else class="empty-card">
        <div class="empty-content">
          <div class="empty-icon">
            📄
          </div>
          <div class="empty-text">
            未找到优惠券信息
          </div>
          <ElButton type="primary" @click="handleBack">
            返回列表
          </ElButton>
        </div>
      </ElCard>
    </div>
  </div>
</template>

<style scoped>
.coupon-detail-page {
  padding: 20px;
  min-height: 100vh;
  background: var(--el-bg-color-page);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.page-subtitle {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.detail-content {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-card,
.empty-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  color: var(--el-text-color-regular);
  margin-bottom: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .coupon-detail-page {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    justify-content: center;
  }

  .page-title {
    font-size: 20px;
  }
}

/* 动画效果 */
.detail-content {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片悬停效果 */
.page-header:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}
</style>
