/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AccountButton: typeof import('./../components/AccountButton/index.vue')['default']
    AudioUpload: typeof import('./../components/AudioUpload/index.vue')['default']
    CinemaSelector: typeof import('./../components/cinemaSelector/index.vue')['default']
    CommonUpload: typeof import('./../components/CommonUpload/index.vue')['default']
    copy: typeof import('./../components/DictSelector copy/index.vue')['default']
    DepartmentSelector: typeof import('./../components/DepartmentSelector/index.vue')['default']
    DictSelector: typeof import('./../components/DictSelector/index.vue')['default']
    DictTag: typeof import('./../components/DictTag/index.vue')['default']
    FaAuth: typeof import('./../ui/components/FaAuth/index.vue')['default']
    FaAvatar: typeof import('./../ui/components/FaAvatar/index.vue')['default']
    FaBackToTop: typeof import('./../ui/components/FaBackToTop/index.vue')['default']
    FaButton: typeof import('./../ui/components/FaButton/index.vue')['default']
    FaButtonGroup: typeof import('./../ui/components/FaButtonGroup/index.vue')['default']
    FaCard: typeof import('./../ui/components/FaCard/index.vue')['default']
    FaCheckbox: typeof import('./../ui/components/FaCheckbox/index.vue')['default']
    FaContextMenu: typeof import('./../ui/components/FaContextMenu/index.vue')['default']
    FaCopyright: typeof import('./../ui/components/FaCopyright/index.vue')['default']
    FaDivider: typeof import('./../ui/components/FaDivider/index.vue')['default']
    FaDrawer: typeof import('./../ui/components/FaDrawer/index.vue')['default']
    FaDropdown: typeof import('./../ui/components/FaDropdown/index.vue')['default']
    FaFixedActionBar: typeof import('./../ui/components/FaFixedActionBar/index.vue')['default']
    FaHoverCard: typeof import('./../ui/components/FaHoverCard/index.vue')['default']
    FaIcon: typeof import('./../ui/components/FaIcon/index.vue')['default']
    FaImagePreview: typeof import('./../ui/components/FaImagePreview/index.vue')['default']
    FaInput: typeof import('./../ui/components/FaInput/index.vue')['default']
    FaKbd: typeof import('./../ui/components/FaKbd/index.vue')['default']
    FaModal: typeof import('./../ui/components/FaModal/index.vue')['default']
    FaNotAllowed: typeof import('./../ui/components/FaNotAllowed/index.vue')['default']
    FaNotification: typeof import('./../ui/components/FaNotification/index.vue')['default']
    FaPageHeader: typeof import('./../ui/components/FaPageHeader/index.vue')['default']
    FaPageMain: typeof import('./../ui/components/FaPageMain/index.vue')['default']
    FaPasswordStrength: typeof import('./../ui/components/FaPasswordStrength/index.vue')['default']
    FaPinInput: typeof import('./../ui/components/FaPinInput/index.vue')['default']
    FaPopover: typeof import('./../ui/components/FaPopover/index.vue')['default']
    FaProgress: typeof import('./../ui/components/FaProgress/index.vue')['default']
    FaScrollArea: typeof import('./../ui/components/FaScrollArea/index.vue')['default']
    FaSearchBar: typeof import('./../ui/components/FaSearchBar/index.vue')['default']
    FaSelect: typeof import('./../ui/components/FaSelect/index.vue')['default']
    FaSlider: typeof import('./../ui/components/FaSlider/index.vue')['default']
    FaSmartFixedBlock: typeof import('./../ui/components/FaSmartFixedBlock/index.vue')['default']
    FaSwitch: typeof import('./../ui/components/FaSwitch/index.vue')['default']
    FaSystemInfo: typeof import('./../ui/components/FaSystemInfo/index.vue')['default']
    FaTabs: typeof import('./../ui/components/FaTabs/index.vue')['default']
    FaToast: typeof import('./../ui/components/FaToast/index.vue')['default']
    FaTooltip: typeof import('./../ui/components/FaTooltip/index.vue')['default']
    FileUpload: typeof import('./../components/FileUpload/index.vue')['default']
    FilmSelector: typeof import('./../components/FilmSelector/index.vue')['default']
    FilmVersion: typeof import('./../components/filmVersion/index.vue')['default']
    HallSelector: typeof import('./../components/hallSelector/index.vue')['default']
    ImagesUpload: typeof import('./../components/ImagesUpload/index.vue')['default']
    ImageUpload: typeof import('./../components/ImageUpload/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    VideoUpload: typeof import('./../components/VideoUpload/index.vue')['default']
    WeekTime: typeof import('./../components/WeekTime/index.vue')['default']
  }
}
