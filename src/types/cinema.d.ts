// 影院管理相关类型定义

// ==================== 影片管理类型 ====================

// 影片演职人员
export interface FilmMarker {
  code: string
  names: string[]
  role: string
}

// 影片信息
export interface Film {
  id: string
  cinemaCode: string
  code: string
  name: string
  lang: string
  duration: number
  sequence: number
  version: string
  publishDate: number
  markers: FilmMarker[]
  introduction: string
  provider: string
}

// 影片搜索请求参数
export interface FilmSearchParams {
  page?: number
  size?: number
  filmName?: string
  version?: string
  provider?: string
}

// 影片搜索响应
export interface FilmSearchResponse {
  total: number
  content: Film[]
  result: Film[]
}

// ==================== 场次管理类型 ====================

// 场次中的影片信息
export interface ScheduleFilm {
  id: string
  code: string
  name: string
  lang: string
  duration: number
  sequence: number
  version: string
  publishDate: string
  publisher: string
  producer: string
  director: string
  cast: string
  introduction: string
  provider: string
  mtime: number
}

// 场次价格信息
export interface SchedulePrice {
  id: string
  lowestPrice: number
  standardPrice: number
  listingPrice: number
  serviceAddFee: number
  cinemaAllowance: number
  mtime: number
}

// 场次座位信息
export interface ScheduleSeat {
  id: string
  featureAppNo: string
  seatCode: string
  rowNum: number
  columnNum: number
  status: 'Available' | 'Locked' | 'Sold' | 'Booked' | 'Unavailable' | 'Isolate'
  memberLevelCode: string
  xCoord: number
  yCoord: number
  level: number
  levelName: string
  provider: string
  mtime: number
}

// 场次信息
export interface Schedule {
  id: string
  code: string
  cinemaCode: string
  screenCode: string
  startTime: number
  playthroughFlag: 'Yes' | 'No'
  marketingCode: string
  marketingName: string
  films: ScheduleFilm[]
  price: SchedulePrice
  seats: ScheduleSeat[]
  provider: string
  ctime: number
  uptime: number
}

// 场次搜索请求参数
export interface ScheduleSearchParams {
  page?: number
  size?: number
  cinemaCode?: string
}

// 场次搜索响应
export interface ScheduleSearchResponse {
  total: number
  content: Schedule[]
  result: Schedule[]
}

// ==================== 影厅管理类型 ====================

// 影厅信息
export interface Screen {
  id: string
  cinemaCode: string
  screenCode: string
  screenName: string
  screenType: string
  seatCount: number
  status: number
  provider: string
  ctime: number
  mtime: number
}

// 影厅搜索请求参数
export interface ScreenSearchParams {
  page?: number
  size?: number
  cinemaCode?: string
  screenName?: string
}

// ==================== 票务相关类型 ====================

// 票务统计
export interface TicketStats {
  totalTickets: number
  soldTickets: number
  revenue: number
  occupancyRate: number
  date: string
}

// 座位锁定状态
export interface SeatLockStatus {
  seatCode: string
  isLocked: boolean
  lockTime?: number
  lockExpireTime?: number
}

// ==================== 营销活动类型 ====================

// 营销活动
export interface Marketing {
  id: string
  marketingCode: string
  marketingName: string
  description: string
  startTime: number
  endTime: number
  status: 'Active' | 'Inactive' | 'Expired'
  cinemaCode: string
  provider: string
  ctime: number
  mtime: number
}

// 营销活动搜索参数
export interface MarketingSearchParams {
  page?: number
  size?: number
  cinemaCode?: string
  activityName?: string
  status?: string
}

// ==================== 影院管理类型 ====================

export interface res {
  data?: any
  msg?: string
  code: number
}
// 影厅信息
export interface Screen {
  code: string
  name: string
  seatCount: number
  type: string
  status?: 'Active' | 'Inactive'
  cinemaCode?: string
}

// 影院信息
export interface Cinema {
  id: string
  name: string
  code?: string
  province?: string
  city?: string
  provider?: string
  address?: string
  phone?: string
  email?: string
  website?: string
  description?: string
  status?: number
  createTime?: string
  updateTime?: string
  contactPhone?: string
  screenCount?: number
  priceType?: number
  version?: number
  syncStatus?: number
  createDate?: string
  ctime?: string
  uptime?: string
  qrCodeUrl?: string
  screens?: Screen[]
}

// 影院搜索参数
export interface CinemaSearchParams {
  page: number
  size: number
  cinemaName?: string
  cinemaCode?: string
  city?: string
  provider?: string
}

// 影院搜索响应
export interface CinemaSearchResponse {
  total: number
  content: Cinema[]
  result?: Cinema[]
}

// ==================== 通用响应类型 ====================

export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

export interface PageResponse<T = any> {
  total: number
  content: T[]
  result?: T[]
}
