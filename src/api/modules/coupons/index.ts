import type { AxiosResponse } from 'axios'
import api from '@/api'

// 优惠券列表查询参数
export interface CouponListParams {
  page?: number
  size?: number
  id?: number | string
  name?: string
  useOn?: number
  couponType?: number
  cinemaScope?: number
  cinemaId?: number | string
  startTime?: string
  endTime?: string
  useStatus?: number
  num?: number
  generateType?: number
  channelId?: number | string
  productId?: number
  channelCode?: string
}

// 优惠券价格规则
export interface PriceTier {
  priceOrigin: number
  priceReduce: number
  priceDiff: number
}

export interface PriceRule {
  priceTiers: PriceTier[]
  matchRule: number
}

// 适用渠道
export interface Channel {
  channelId: string
  channelName: string
}

export interface ChannelRule {
  channelScope: number
  channels: Channel[]
}

// 生成规则
export interface GenerateRule {
  generateType: number
  num: number
}

// 结算规则
export interface Priced {
  type: number
  money: number
}

export interface SettleRule {
  method: number
  priced: Priced
}

// 时间有效规则
export interface PeriodRule {
  validScope: number
  overdueDay: number
  startTime: number
  endTime: number
}

// 优惠券列表响应项
export interface CouponItem {
  id: string
  name: string
  useOn: number
  reduction: number
  userServiceFee: number
  note: string
  remark: string
  couponType: number
  priceRule: PriceRule
  channelRule: ChannelRule
  generateRule: GenerateRule
  settleRule: SettleRule
  periodRule: PeriodRule
  createTime: number
  updatedTime: number
  bindCount: number
  usedCount: number
  useStatus: number
  generateStatus: number
  deleted: boolean
}

// 优惠券列表响应
export interface CouponListResponse {
  total: number
  content: CouponItem[]
  result: CouponItem[]
}

// 查询优惠券列表
export function getCouponList(params: CouponListParams) {
  return api.post<AxiosResponse<CouponListResponse>>('/adm_coupon/list', params)
}

// 创建优惠券参数
export interface CreateCouponParams {
  name?: string
  useOn?: number
  couponType?: number
  reduction?: number
  userServiceFee?: number
  priceRule?: PriceRule
  channelRule?: ChannelRule
  generateRule?: GenerateRule
  settleRule?: SettleRule
  periodRule?: PeriodRule
  note?: string
  valuable?: number
  remark?: string
}

// 创建优惠券
export function createCoupon(params: CreateCouponParams): Promise<any> {
  return api.post('/adm_coupon/add', params)
}

// 优惠券状态变更参数
export interface CouponStatusParams {
  id: string
  useStatus: number
}

// 优惠券状态变更
export function changeCouponStatus(params: CouponStatusParams) {
  return api.post('/adm_coupon/status', params)
}

// 绑定优惠券参数
export interface BindCouponParams {
  id: string
  userIds?: number[]
  mobiles: number[]
  num?: number
  bindType?: number
  codeId: string
}

// 绑定优惠券
export function bindCoupon(params: BindCouponParams) {
  return api.post('/adm_coupon/bind', params)
}

// 创建优惠券限制规则参数
export interface CouponRestrictionParams {
  couponId?: string
  useOn?: number
  ticketRestriction?: {
    id?: string
    cinemaRestriction?: {
      cinemaScope?: number
      cinemas?: {
        id?: number
        cinemaId?: string
        cinemaName?: string
        halls?: {
          hallId?: string
          hallName?: string
        }[]
      }[]
    }
    filmRestriction?: {
      filmScope?: number
      films?: {
        id?: number
        filmId?: number
        filmCode?: string
        filmName?: string
      }[]
    }
    periodRestriction?: {
      periodScope?: number
      periods?: {
        id?: number
        num?: number
        day?: number
        start?: number
        end?: number
      }[]
    }
    filmVersionRestriction?: {
      versionScope?: number
      versions?: {
        filmVersion?: string
        versionId?: string
      }[]
    }
  }
  showRestrictions?: {
    showScope: number
    shows?: {
      cinemaId?: number
      cinemaName?: string
      showName?: string
      showId?: string
      id?: string
      sessions?: {
        id?: string
        showScheduleId: number
        startTime: string
        endTime: string
      }[]
    }[]
  }
  goodsRestrictions?: {
    goodsScope: number
    cinemas?: {
      id?: number
      cinemaId?: string
      cinemaName?: string
      halls?: {
        hallId?: string
        hallName?: string
      }[]
    }[]
    goodsTypeIds?: number[]
    goods?: {
      id: number
      cinemaId: number
      cinemaName: string
      goodsId: string
      goodsName: string
    }[]
  }
}

// 创建优惠券限制规则
export function createCouponRestriction(params: CouponRestrictionParams) {
  return api.post('/adm_coupon/createRestriction', params)
}

export function getCouponRestriction(params: CouponRestrictionParams) {
  return api.post<CouponRestrictionParams>('/adm_coupon/getRestriction', params)
}

// 获取优惠券详情
export function getCouponDetail(couponId: string) {
  return api.post(`/adm_coupon/getCoupon`, { couponId }, {
    // headers: {
    //   'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
    // },
  })
}

// 更新优惠券参数
export interface UpdateCouponParams extends CreateCouponParams {
  id: string
  useOn?: number
  couponType?: number
  reduction?: number
  userServiceFee?: number
}

// 更新优惠券
export function updateCoupon(params: UpdateCouponParams): Promise<any> {
  return api.post('/adm_coupon/save', params)
}

// 删除优惠券
export function deleteCoupon(id: string): Promise<any> {
  return api.post('/adm_coupon/delete', { id })
}

// 获取优惠券码列表
export function getCouponCodes(couponId: string, params?: any): Promise<any> {
  return api.post('/adm_coupon/codes', { couponId, ...params })
}

// 导出优惠券数据
export function exportCoupons(params: CouponListParams) {
  return api.post('/adm_coupon/export', params, {
    responseType: 'blob',
  })
}

// 批量删除优惠券
export function batchDeleteCoupons(ids: string[]) {
  return api.post('/adm_coupon/batch/delete', { ids })
}

// 批量更新优惠券状态
export function batchUpdateCouponStatus(ids: string[], useStatus: number) {
  return api.post('/adm_coupon/batch/status', { ids, useStatus })
}

// 复制优惠券
export function copyCoupon(id: string, name: string) {
  return api.post('/adm_coupon/copy', { id, name })
}

// 获取优惠券统计信息
export function getCouponStatistics(params?: any) {
  return api.post('/adm_coupon/statistics', params)
}

// 手动生成优惠券码
export function generateCouponCodes(couponId: string, count: number) {
  return api.post('/adm_coupon/codes/generate', { couponId, count })
}

export default {
  getCouponList,
  getCouponDetail,
  createCoupon,
  updateCoupon,
  deleteCoupon,
  changeCouponStatus,
  bindCoupon,
  getCouponCodes,
  exportCoupons,
  batchDeleteCoupons,
  batchUpdateCouponStatus,
  copyCoupon,
  getCouponStatistics,
  generateCouponCodes,
  createCouponRestriction,
  getCouponRestriction,
}
