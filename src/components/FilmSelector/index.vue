<!--
 * 作者：zhang
 * 时间：2023/10/27 9:55
 * 功能：
 *
-->
<script setup>
// import {getCurrentInstance, onMounted, ref} from "vue";
import { getFilmList } from '@/api/modules/film/film.js'

const props = defineProps({
  // 绑定的影片信息
  modelValue: {
    type: Object,
    default: () => {
      return {}
    },
  },
})
const proxy = getCurrentInstance()
const dialogVisible = ref(false)

const filmDataList = ref([])
// 选中的影片
const selectedFilms = ref([])

const total = ref(0)

const searchForm = ref({
  name: '',
  releaseStatus: '', // 影片上映状态 0即将上映 1热映中 2已下映
  pageSize: 10,
  pageNum: 1,
})

onMounted(() => {
  console.log('执行查询')
  selectedFilms.value = props.modelValue
  query()
})
function query() {
  getFilmList(searchForm.value).then((res) => {
    const { total: newTotal, rows, code } = res
    if (code === 200) {
      filmDataList.value = rows.map((item) => {
        return {
          id: item.id,
          name: item.name,
          code: item.code,
          status: item.releaseStatus,
        }
      })
      total.value = newTotal
    }
  })
}
function handleSelectionChange(films) {
  console.log(films)
  selectedFilms.value = films
  // 1.如果films的长度大于0，说明有选中的影片
  // 2.如果films的长度等于0，说明没有选中的影片，需要拿filmDataList.value去匹配modelValue，
  // 如果匹配到了，就删除
  // console.log("selectedFilms", selectedFilms.value);
  // proxy.emit("update:modelValue", selectedFilms.value);
}

// 确认选中影片
function confirm() {
  dialogVisible.value = false
  proxy.emit('update:modelValue', selectedFilms.value)
}

const multipleTableRef = ref(null)
// 取消选中某一个影片
function cancel(film) {
  console.log(film)
  selectedFilms.value = selectedFilms.value.filter((item) => {
    return item !== film
  })
  multipleTableRef.value.toggleRowSelection(film, false)
  // console.log(multipleTableRef.value)
}

function removeTag(film) {
  selectedFilms.value = selectedFilms.value.filter((item) => {
    return item !== film
  })
  // console.log(multipleTableRef.value)
  // console.log(multipleTableRef.value)
  // multipleTableRef.value.toggleRowSelection(film, false);
  proxy.emit('update:modelValue', selectedFilms.value)
}
</script>

<template>
  <div>
    <el-row>
      <el-col :span="24">
        <slot name="leftSlot" />
        <el-button type="primary" plain @click="dialogVisible = true">
          选择影片
        </el-button>
        <el-card
          v-show="selectedFilms.length > 0"
          shadow="never"
          style="margin-top: 10px;"
          :body-style="{ padding: '0' }"
        >
          <el-row>
            <el-col :span="24">
              <el-tag
                v-for="film in modelValue"
                size="small"
                style="margin-left: 10px;"
                closable
                @close="removeTag(film)"
              >
                {{ film.name }}
              </el-tag>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
    <div>
      <el-dialog v-model="dialogVisible" width="80%" draggable>
        <template #header>
          请选择影片
          <el-divider />
          <el-alert
            title="定价方式修改后，可能会影响与影城的结算价、用户的购买价，请认真设置！"
            type="warning"
            :closable="false"
            show-icon
          />
        </template>

        <div>
          <el-row :gutter="20">
            <el-col :span="16">
              <el-form v-model="searchForm" inline>
                <el-form-item label="影片名称:">
                  <el-input
                    v-model="searchForm.name"
                    placeholder="请输入影片名称"
                    style="width: 15vw;"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="影片状态:">
                  <el-select
                    v-model="searchForm.releaseStatus"
                    placeholder="请选择影片状态"
                    style="width: 10vw;"
                    clearable
                  >
                    <el-option label="即将上映" value="0" />
                    <el-option label="热映中" value="1" />
                    <el-option label="已下映" value="2" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" plain @click="query">
                    查询
                  </el-button>
                </el-form-item>
              </el-form>
              <el-table
                ref="multipleTableRef"
                :data="filmDataList"

                stripe border
                :row-key="row => row.code"
                style="margin-top: 12px;"
                @selection-change="handleSelectionChange"
              >
                <el-table-column
                  type="selection"
                  align="center"
                  width="55"
                  :reserve-selection="true"
                />
                <el-table-column>
                  <template #default="{ row }">
                    <el-text size="large">
                      {{ row.name }}
                    </el-text>
                  </template>
                </el-table-column>
                <!-- 影片编码 -->
                <el-table-column
                  prop="code"
                  label="影片编码"
                  align="center"
                  width="150"
                />
                <!-- 影片状态status 影片上映状态 0即将上映 1热映中 2已下映 -->
                <el-table-column label="影片状态" align="center" width="150">
                  <template #default="{ row }">
                    <!--                    <el-tag -->
                    <!--                        :type="row.releaseStatus === 1 ? 'success' : 'danger'" -->
                    <!--                        size="small" -->
                    <!--                    > -->
                    <!--                      {{ row.status === 1 ? "上架" : "下架" }} -->
                    <!--                    </el-tag> -->
                    <el-tag v-if="row.status === '0'" size="small">
                      即将上映
                    </el-tag>
                    <el-tag v-else-if="row.status === '1'" type="success" size="small">
                      热映中
                    </el-tag>
                    <el-tag v-else type="danger" size="small">
                      下架
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
              <!-- <el-pagination
                  layout="total, sizes, prev, pager, next, jumper"
                  v-model:page="searchForm.pageNum"
                  v-model:limit="searchForm.pageSize"
                  @pagination="query"
              /> -->
              <el-pagination
                v-model:page="searchForm.pageNum"
                v-model:limit="searchForm.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                @pagination="query"
              />
            </el-col>
            <el-col :span="8" style="padding-top: 48px;">
              <el-table :data="selectedFilms" border style="height: 550px;">
                <el-table-column prop="name" label="影片名称" />
                <!--              删除 -->
                <el-table-column label="操作" align="center" width="150">
                  <template #default="{ row }">
                    <el-button
                      type="danger"
                      icon="Delete"
                      size="small"
                      plain
                      @click="cancel(row)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </div>
        <template #footer>
          <el-button @click="dialogVisible = false">
            取 消
          </el-button>
          <el-button type="primary" @click="confirm">
            确 定
          </el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>
