<script setup lang="ts">
import type { Cinema, CinemaSearchParams } from '@/types/cinema'
import { ElButton, ElMessage } from 'element-plus'
// import { computed, getCurrentInstance, nextTick, onMounted, ref, watch } from 'vue'
import cinemaApi from '@/api/modules/cinema/index.ts'
import { useFaModal } from '@/ui/components/FaModal'

// ==================== 类型定义 ====================
interface SelectedCinema {
  id: string
  name: string
  aliasName?: string
  code?: string
  cityName?: string
  [key: string]: any
}

interface Props {
  modelValue: SelectedCinema[] // 选中的影院数据
  multiple?: boolean // 是否多选
  showSelectNum?: boolean // 是否显示已选择数量
  placeholder?: string // 占位符
  disabled?: boolean // 是否禁用
}

interface Emits {
  (e: 'update:modelValue', value: SelectedCinema[]): void
  (e: 'change', value: SelectedCinema[]): void
}

// ==================== Props & Emits ====================
const props = withDefaults(defineProps<Props>(), {
  //
  multiple: true,
  showSelectNum: true,
  placeholder: '请选择影院',
  disabled: false,
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================
const dialogVisible = ref(false)
const isLoading = ref(false)
const cinemaList = ref<Cinema[]>([])
const selectedCinemas = ref<SelectedCinema[]>([])
const total = ref(0)

// 搜索表单
const searchForm = ref<CinemaSearchParams>({
  page: 1,
  size: 10,
  cinemaName: '',
  city: '',
})

// 表格引用
const tableRef = ref()

// ==================== 计算属性 ====================
const selectedCount = computed(() => selectedCinemas.value.length)

const displayText = computed(() => {
  if (selectedCount.value === 0) {
    return props.placeholder
  }
  if (props.showSelectNum) {
    return `已选择 ${selectedCount.value} 个影院`
  }
  return selectedCinemas.value.map(item => item.name).join(', ')
})

// ==================== 数据初始化 ====================
// 监听 modelValue 变化，同步到内部状态
watch(() => props.modelValue, (newValue) => {
  selectedCinemas.value = [...newValue]
}, { immediate: true, deep: true })

// ==================== 核心功能函数 ====================

/**
 * 获取影院列表
 */
async function fetchCinemaList() {
  try {
    isLoading.value = true
    const response = await cinemaApi.getCinemaList(searchForm.value)

    if (response.code === 0) {
      const { content, total: totalCount } = response.data
      cinemaList.value = content || []
      total.value = totalCount || 0

      // 更新表格选中状态
      await nextTick()
      updateTableSelection()
    }
    else {
      ElMessage.error(response.msg || '获取影院列表失败')
    }
  }
  catch (error) {
    console.error('获取影院列表失败:', error)
    ElMessage.error('获取影院列表失败')
  }
  finally {
    isLoading.value = false
  }
}

/**
 * 更新表格选中状态
 */
function updateTableSelection() {
  if (!tableRef.value) { return }

  const selectedIds = selectedCinemas.value.map(item => item.id)
  cinemaList.value.forEach((cinema) => {
    const isSelected = selectedIds.includes(cinema.code || cinema.id)
    tableRef.value.toggleRowSelection(cinema, isSelected)
  })
}

/**
 * 搜索影院
 */
function handleSearch() {
  searchForm.value.page = 1
  fetchCinemaList()
}

/**
 * 重置搜索条件
 */
function handleReset() {
  searchForm.value = {
    page: 1,
    size: 10,
    cinemaName: '',
    city: '',
  }
  fetchCinemaList()
}
/**
 * 分页变化处理
 */
function handlePageChange(page: number) {
  searchForm.value.page = page
  fetchCinemaList()
}

/**
 * 分页大小变化处理
 */
function handleSizeChange(size: number) {
  searchForm.value.size = size
  searchForm.value.page = 1
  fetchCinemaList()
}

// ==================== 选择相关功能 ====================

/**
 * 转换影院数据格式
 */
function transformCinemaData(cinema: Cinema): SelectedCinema {
  return {
    id: cinema.id, // 使用 code 作为唯一标识
    name: cinema.name,
    // aliasName: cinema.name, // 如果没有别名，使用名称
    // code: cinema.code || '',
    // cityName: cinema.city || '',
  }
}

/**
 * 添加影院到选中列表
 */
function addCinema(cinema: Cinema) {
  const cinemaData = transformCinemaData(cinema)

  // 检查是否已选择
  if (selectedCinemas.value.some(item => item.id === cinemaData.id)) {
    ElMessage.warning('该影院已被选择')
    return
  }

  if (props.multiple) {
    selectedCinemas.value.push(cinemaData)
    console.log(selectedCinemas.value)
  }
  else {
    selectedCinemas.value = [cinemaData]
  }

  updateTableSelection()
}

/**
 * 从选中列表移除影院
 */
function removeCinema(cinema: SelectedCinema) {
  const index = selectedCinemas.value.findIndex(item => item.id === cinema.id)
  if (index > -1) {
    selectedCinemas.value.splice(index, 1)
    updateTableSelection()
  }
}

/**
 * 批量添加当前页所有影院
 */
function addAllCurrentPage() {
  cinemaList.value.forEach((cinema) => {
    const cinemaData = transformCinemaData(cinema)
    if (!selectedCinemas.value.some(item => item.id === cinemaData.id)) {
      selectedCinemas.value.push(cinemaData)
    }
  })
  updateTableSelection()
  ElMessage.success(`已添加 ${cinemaList.value.length} 个影院`)
}

/**
 * 清空所有选择
 */
function clearAll() {
  useFaModal().confirm({
    title: '提示',
    content: '确认清空所有选择吗？',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    onConfirm: () => {
      selectedCinemas.value = []
      updateTableSelection()
      ElMessage.success('已清空所有选择')
    },
  })
}

// ==================== 对话框相关功能 ====================

/**
 * 打开选择对话框
 */
function openDialog() {
  if (props.disabled) { return }

  dialogVisible.value = true
  // 重置搜索条件并加载数据
  handleReset()
}

/**
 * 关闭对话框
 */
function closeDialog() {
  dialogVisible.value = false
}

/**
 * 确认选择
 */
function confirmSelection() {
  // 更新外部值
  emit('update:modelValue', [...selectedCinemas.value])
  emit('change', [...selectedCinemas.value])

  closeDialog()
  ElMessage.success(`已选择 ${selectedCinemas.value.length} 个影院`)
}

/**
 * 取消选择，恢复到打开对话框前的状态
 */
function cancelSelection() {
  selectedCinemas.value = [...props.modelValue]
  closeDialog()
}

// ==================== 生命周期 ====================
onMounted(() => {
  // 组件挂载时不自动加载数据，只在打开对话框时加载
})

defineExpose({
  openDialog,
})
</script>

<template>
  <div class="cinema-selector">
    <!-- 触发按钮 -->
    <slot>
      <ElButton
        type="primary"
        :disabled="disabled"
        @click="openDialog"
      >
        选择影院
      </ElButton>
      <span v-if="showSelectNum && selectedCount > 0" class="selected-count">
        {{ displayText }}
      </span>
    </slot>

    <!-- 选择对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="选择影院"
      width="90%"
      draggable
      :z-index="1999"
      destroy-on-close
    >
      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="影院名称">
            <el-input
              v-model="searchForm.cinemaName"
              placeholder="请输入影院名称"
              clearable
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item label="城市">
            <el-input
              v-model="searchForm.city"
              placeholder="请输入城市"
              clearable
              style="width: 150px;"
            />
          </el-form-item>
          <el-form-item>
            <ElButton type="primary" @click="handleSearch">
              <fa-icon name="ep:search" />
              搜索
            </ElButton>
            <ElButton @click="handleReset">
              <fa-icon name="ep:refresh" />
              重置
            </ElButton>
          </el-form-item>
        </el-form>
      </div>

      <!-- 主要内容区域 -->
      <div class="dialog-content">
        <el-row :gutter="20">
          <!-- 左侧：影院列表 -->
          <el-col :span="16">
            <el-table
              ref="tableRef"
              v-loading="isLoading"
              :data="cinemaList"

              stripe border
              size="small"
              :row-key="(row) => row.code"
              style="width: 100%;"
            >
              <el-table-column prop="name" label="影院名称" min-width="150">
                <template #default="{ row }">
                  <el-text>{{ row.name }}</el-text>
                </template>
              </el-table-column>

              <el-table-column prop="code" label="影院编码" width="120" align="center">
                <template #default="{ row }">
                  <el-text type="info">
                    {{ row.code }}
                  </el-text>
                </template>
              </el-table-column>

              <el-table-column prop="city" label="城市" width="100" align="center">
                <template #default="{ row }">
                  <el-text>{{ row.city || '-' }}</el-text>
                </template>
              </el-table-column>

              <el-table-column prop="provider" label="供应商" width="120" align="center">
                <template #default="{ row }">
                  <el-text>{{ row.provider || '-' }}</el-text>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="80" align="center" fixed="right">
                <template #default="{ row }">
                  <ElButton
                    circle type="primary"
                    :disabled="selectedCinemas.some(item => item.id === (row.code || row.name))"
                    @click="addCinema(row)"
                  >
                    <fa-icon name="ep:plus" />
                  </ElButton>
                </template>
              </el-table-column>
            </el-table>

            <!-- 批量操作 -->
            <div class="batch-actions">
              <ElButton
                type="primary"
                size="small"
                plain
                :disabled="cinemaList.length === 0"
                @click="addAllCurrentPage"
              >
                批量添加本页
              </ElButton>
            </div>

            <!-- 分页 -->
            <el-pagination
              v-model:current-page="searchForm.page"
              v-model:page-size="searchForm.size"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              style="margin-top: 16px;"
              @size-change="handleSizeChange"
              @current-change="handlePageChange"
            />
          </el-col>

          <!-- 右侧：已选择的影院 -->
          <el-col :span="8">
            <div class="selected-panel">
              <div class="selected-header">
                <span class="selected-title">已选择的影院 ({{ selectedCount }})</span>
                <ElButton
                  v-if="selectedCount > 0"
                  type="danger"
                  size="small"
                  plain
                  @click="clearAll"
                >
                  清空全部
                </ElButton>
              </div>

              <el-scrollbar height="400px">
                <div v-if="selectedCount === 0" class="empty-state">
                  <el-empty description="暂无选择的影院" :image-size="80" />
                </div>

                <div v-else class="selected-list">
                  <div
                    v-for="cinema in selectedCinemas"
                    :key="cinema.id"
                    class="selected-item"
                  >
                    <div class="cinema-info">
                      <div class="cinema-name">
                        {{ cinema.name }}
                      </div>
                      <div class="cinema-details">
                        <span class="cinema-code">{{ cinema.code }}</span>
                        <span v-if="cinema.cityName" class="cinema-city">{{ cinema.cityName }}</span>
                      </div>
                    </div>
                    <ElButton
                      type="danger"
                      circle
                      plain
                      @click="removeCinema(cinema)"
                    >
                      <fa-icon name="ep:delete" />
                    </ElButton>
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 对话框底部 -->
      <template #footer>
        <div class="dialog-footer">
          <span class="selected-summary">
            已选择 {{ selectedCount }} 个影院
          </span>
          <div class="footer-buttons">
            <ElButton @click="cancelSelection">
              取消
            </ElButton>
            <ElButton type="primary" @click="confirmSelection">
              确定选择
            </ElButton>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.cinema-selector {
  display: inline-flex;
  gap: 8px;
  align-items: center;

  .selected-count {
    font-size: 14px;
    color: var(--el-text-color-regular);
  }
}

.search-form {
  padding: 16px;
  margin-bottom: 16px;
  background-color: var(--el-bg-color-page);
  border-radius: 6px;
}

.dialog-content {
  .batch-actions {
    margin-top: 12px;
    text-align: right;
  }
}

.selected-panel {
  overflow: hidden;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;

  .selected-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: var(--el-bg-color-page);
    border-bottom: 1px solid var(--el-border-color);

    .selected-title {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }

  .empty-state {
    padding: 40px 20px;
    text-align: center;
  }

  .selected-list {
    padding: 8px;
  }

  .selected-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    margin-bottom: 8px;
    background-color: var(--el-fill-color-lighter);
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      background-color: var(--el-fill-color-light);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .cinema-info {
      flex: 1;
      min-width: 0;

      .cinema-name {
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 500;
        color: var(--el-text-color-primary);
        white-space: nowrap;
      }

      .cinema-details {
        display: flex;
        gap: 8px;
        font-size: 12px;
        color: var(--el-text-color-regular);

        .cinema-code {
          padding: 2px 6px;
          background-color: var(--el-color-info-light-9);
          border-radius: 3px;
        }

        .cinema-city {
          color: var(--el-text-color-secondary);
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .selected-summary {
    font-size: 14px;
    color: var(--el-text-color-regular);
  }

  .footer-buttons {
    display: flex;
    gap: 12px;
  }
}

// 响应式设计
@media (width <= 768px) {
  .cinema-selector {
    .selected-count {
      display: block;
      margin-top: 4px;
    }
  }

  .dialog-content {
    .el-row {
      flex-direction: column;

      .el-col {
        width: 100% !important;
        margin-bottom: 16px;
      }
    }
  }

  .selected-panel {
    .selected-item {
      .cinema-info {
        .cinema-details {
          flex-direction: column;
          gap: 4px;
        }
      }
    }
  }
}
</style>
