<script lang="ts" setup>
import { ElMessage } from 'element-plus'
// import { computed, onMounted, ref, watch } from 'vue'
import cinemaApi from '@/api/modules/cinema'

interface Hall {
  code: string
  name: string
  seatCount?: number
  type: string
}

interface Props {
  cinemaId?: string
  cinemaCode?: string
  cinemaName?: string
  modelValue: Hall[] // 选中的影厅code数组
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void

  (e: 'change', value: string[], halls: Hall[]): void
}

const props = withDefaults(defineProps<Props>(), {
  cinemaId: '',
  cinemaCode: '',
  cinemaName: '',
  disabled: false, // 是否禁用选择
})

const emit = defineEmits<Emits>()

// 响应式数据
const isLoading = ref(false)
const hallList = ref<Hall[]>([])
const selectedHalls = ref<string[]>([...props.modelValue])

// 计算属性
const selectAll = computed({
  get: () => selectedHalls.value.length === hallList.value.length && hallList.value.length > 0,
  set: (value: boolean) => {
    if (value) {
      selectedHalls.value = hallList.value.map(hall => hall.code)
    }
    else {
      selectedHalls.value = []
    }
  },
})

const isIndeterminate = computed(() => {
  const selectedCount = selectedHalls.value.length
  return selectedCount > 0 && selectedCount < hallList.value.length
})

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  selectedHalls.value = [...newValue]
}, { deep: true })

watch(() => props.cinemaCode, (newCode) => {
  if (newCode) {
    fetchHallList()
  }
}, { immediate: true })

// 监听选择变化
watch(selectedHalls, (newValue) => {
  emit('update:modelValue', newValue)
  const selectedHallObjects = hallList.value.filter(hall => newValue.includes(hall.code))
  emit('change', newValue, selectedHallObjects)
}, { deep: true })

// 方法
async function fetchHallList() {
  if (!props.cinemaCode) {
    return
  }

  try {
    isLoading.value = true
    const response = await cinemaApi.getCinemaDetail({ id: props.cinemaId })

    if (response.code === 0 && response.data) {
      hallList.value = response.data.screens || []
    }
    else {
      ElMessage.error('获取影厅列表失败')
      hallList.value = []
    }
  }
  catch (error) {
    console.error('获取影厅列表失败:', error)
    ElMessage.error('获取影厅列表失败')
    hallList.value = []
  }
  finally {
    isLoading.value = false
  }
}

function handleSelectAll(value: boolean) {
  selectAll.value = value
}

function handleSelectionChange(value: string[]) {
  selectedHalls.value = value
}

// 暴露方法给父组件
defineExpose({
  fetchHallList,
  getSelectedHalls: () => hallList.value.filter(hall => selectedHalls.value.includes(hall.code)),
  getAllHalls: () => hallList.value,
})

onMounted(() => {
  if (props.cinemaCode) {
    fetchHallList()
  }
})
</script>

<template>
  <div class="hall-selector">
    <div class="cinema-info">
      <el-text class="cinema-name">
        {{ cinemaName }}
      </el-text>
      <el-text size="small" type="info">
        {{ cinemaCode }}
      </el-text>
    </div>

    <div class="hall-selection">
      <div class="selection-header">
        <el-checkbox
          v-model="selectAll"
          :indeterminate="isIndeterminate"
          @change="handleSelectAll"
        >
          全选影厅
        </el-checkbox>
        <el-text size="small" type="info">
          已选择 {{ selectedHalls.length }} / {{ hallList.length }} 个影厅
        </el-text>
      </div>

      <div v-loading="isLoading" class="hall-list">
        <div v-if="hallList.length === 0 && !isLoading" class="empty-state">
          <el-empty :image-size="60" description="暂无影厅数据" />
        </div>

        <el-checkbox-group v-else v-model="selectedHalls" @change="handleSelectionChange">
          <div class="hall-grid">
            <el-checkbox
              v-for="hall in hallList"
              :key="hall.code"
              :value="hall.code"
              class="hall-item"
            >
              <div class="hall-content">
                <div class="hall-name">
                  {{ hall.name }}
                </div>
                <div class="hall-details">
                  <el-tag size="small" type="info">
                    {{ hall.type || '标准厅' }}
                  </el-tag>
                  <span class="seat-count">{{ hall.seatCount }}座</span>
                </div>
              </div>
            </el-checkbox>
          </div>
        </el-checkbox-group>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.hall-selector {
  overflow: hidden;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;

  .cinema-info {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 12px 16px;
    background-color: var(--el-bg-color-page);
    border-bottom: 1px solid var(--el-border-color);

    .cinema-name {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }

  .hall-selection {
    padding: 16px;

    .selection-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 12px;
      margin-bottom: 16px;
      border-bottom: 1px solid var(--el-border-color-lighter);
    }

    .hall-list {
      min-height: 120px;

      .empty-state {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 120px;
      }

      .hall-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 12px;

        .hall-item {
          width: 100%;
          margin: 0;

          :deep(.el-checkbox__label) {
            width: 100%;
            padding-left: 8px;
          }

          .hall-content {
            display: flex;
            flex-direction: column;
            gap: 4px;
            padding: 8px;
            border: 1px solid var(--el-border-color-lighter);
            border-radius: 4px;
            transition: all 0.2s;

            &:hover {
              background-color: var(--el-color-primary-light-9);
              border-color: var(--el-color-primary);
            }

            .hall-name {
              overflow: hidden;
              text-overflow: ellipsis;
              font-weight: 500;
              color: var(--el-text-color-primary);
              white-space: nowrap;
            }

            .hall-details {
              display: flex;
              align-items: center;
              justify-content: space-between;
              font-size: 12px;

              .seat-count {
                color: var(--el-text-color-secondary);
              }
            }
          }
        }

        .hall-item.is-checked {
          .hall-content {
            background-color: var(--el-color-primary-light-9);
            border-color: var(--el-color-primary);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (width <= 768px) {
  .hall-selector {
    .hall-selection {
      .hall-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>
