<script setup lang="ts">
import { computed, ref } from 'vue'

import { toast } from 'vue-sonner'

import apiSystem from '@/api/modules/system/system.ts'

defineOptions({
  name: 'DepartmentSelector',
})

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择上级部门',
  excludeId: '',
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

interface Department {
  id: string
  name: string
  parentId?: string
  sort?: number
  ctime: number
  children?: Department[]
}

interface Props {
  modelValue?: string
  placeholder?: string
  departmentData: Department[]
  excludeId?: string // 排除的部门ID（用于编辑时不能选择自己）
}

// 处理选择变化
function handleChange(value: any) {
  if (value && Array.isArray(value) && value.length > 0) {
    // 取最后一个值作为选中的部门ID
    emit('update:modelValue', value[value.length - 1])
  }
  else {
    emit('update:modelValue', '')
  }
}

// 处理清空
function handleClear() {
  emit('update:modelValue', '')
}

// 监听modelValue变化，转换为 Cascader 需要的数组格式
const cascaderValue = computed(() => {
  // 如果没有选中值或者没有部门数据，返回空数组
  if (!props.modelValue || !props.departmentData || props.departmentData.length === 0) {
    return []
  }

  // 根据选中的部门ID，构建完整的路径数组
  const findPath = (id: string, departments: Department[], path: string[] = []): string[] | null => {
    for (const dept of departments) {
      const currentPath = [...path, dept.id]

      if (dept.id === id) {
        return currentPath
      }

      if (dept.children && dept.children.length > 0) {
        const found = findPath(id, dept.children, currentPath)
        if (found) {
          return found
        }
      }
    }
    return null
  }

  return findPath(props.modelValue, props.departmentData) || []
})

// 构建部门树结构
function buildDepartmentTree(departmentList: Department[]): Department[] {
  const departmentMap = new Map<string, Department>()
  const rootDepartments: Department[] = []

  // 创建部门映射
  departmentList.forEach((department) => {
    departmentMap.set(department.id, { ...department, children: [] })
  })

  // 构建树形结构
  departmentList.forEach((department) => {
    const departmentNode = departmentMap.get(department.id)!
    if (department.parentId && departmentMap.has(department.parentId)) {
      const parentNode = departmentMap.get(department.parentId)!
      parentNode.children!.push(departmentNode)
    }
    else {
      rootDepartments.push(departmentNode)
    }
  })

  return rootDepartments
}

const departmentTreeData: Ref<any[]> = ref([])
const departmentTreeLoading = ref(false)

// 获取部门树数据（用于选择父部门）
async function getDepartmentTree() {
  departmentTreeLoading.value = true
  try {
    const res: any = await apiSystem.getDepartmentList({
      page: 0,
      size: 1000, // 获取所有部门用于构建树
    })
    if (res.code === 0) {
      // 将扁平数据转换为树形结构
      console.log('获取部门数据:', res.data)

      // Convert Department[] to cascader format
      const departmentList: Department[] = res.data.content || []
      console.log('部门列表:', departmentList)
      console.log(buildDepartmentTree(res.data.content))

      departmentTreeData.value = buildDepartmentTree(res.data.content)
    }
    else {
      toast.error(res.msg || '获取部门数据失败')
    }
  }
  catch (error: any) {
    toast.error(error.message || '获取部门数据失败')
  }
  finally {
    departmentTreeLoading.value = false
  }
}

onMounted(() => {
  getDepartmentTree()
})
</script>

<template>
  <div class="department-selector">
    <el-cascader
      v-model="cascaderValue"
      :options="departmentTreeData"
      :props="{
        expandTrigger: 'click',
        value: 'id',
        label: 'name',
        children: 'children',
        emitPath: true,
        checkStrictly: true,
      }"
      :placeholder="placeholder"
      clearable
      filterable
      :show-all-levels="true"
      @change="handleChange"
      @clear="handleClear"
    />
  </div>
</template>

<style scoped>
.department-selector {
  width: 100%;
}

:deep(.el-cascader) {
  width: 100%;
}
</style>
